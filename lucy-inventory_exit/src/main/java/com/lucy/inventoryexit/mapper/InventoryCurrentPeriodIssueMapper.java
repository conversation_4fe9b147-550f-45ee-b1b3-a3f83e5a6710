package com.lucy.inventoryexit.mapper;

import java.util.List;
import com.lucy.inventoryexit.domain.InventoryCurrentPeriodIssue;

/**
 * 本期出库明细 Current period inventory issuesMapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface InventoryCurrentPeriodIssueMapper 
{
    /**
     * 查询本期出库明细 Current period inventory issues
     * 
     * @param id 本期出库明细 Current period inventory issues主键
     * @return 本期出库明细 Current period inventory issues
     */
    public InventoryCurrentPeriodIssue selectInventoryCurrentPeriodIssueById(Long id);

    /**
     * 查询本期出库明细 Current period inventory issues列表
     * 
     * @param inventoryCurrentPeriodIssue 本期出库明细 Current period inventory issues
     * @return 本期出库明细 Current period inventory issues集合
     */
    public List<InventoryCurrentPeriodIssue> selectInventoryCurrentPeriodIssueList(InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue);

    /**
     * 新增本期出库明细 Current period inventory issues
     *
     * @param inventoryCurrentPeriodIssue 本期出库明细 Current period inventory issues
     * @return 结果
     */
    public int insertInventoryCurrentPeriodIssue(InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue);

    /**
     * 批量新增本期出库明细 Current period inventory issues
     *
     * @param inventoryCurrentPeriodIssues 本期出库明细 Current period inventory issues列表
     * @return 结果
     */
    public int batchInsertInventoryCurrentPeriodIssue(List<InventoryCurrentPeriodIssue> inventoryCurrentPeriodIssues);

    /**
     * 修改本期出库明细 Current period inventory issues
     * 
     * @param inventoryCurrentPeriodIssue 本期出库明细 Current period inventory issues
     * @return 结果
     */
    public int updateInventoryCurrentPeriodIssue(InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue);

    /**
     * 删除本期出库明细 Current period inventory issues
     * 
     * @param id 本期出库明细 Current period inventory issues主键
     * @return 结果
     */
    public int deleteInventoryCurrentPeriodIssueById(Long id);

    /**
     * 批量删除本期出库明细 Current period inventory issues
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryCurrentPeriodIssueByIds(Long[] ids);

    /**
     * 根据单据类型查本期出库明细 Current period inventory issues
     *
     * @param document_type 需要删除的数据主键集合
     * @return InventoryCurrentPeriodIssue
     */
    List<InventoryCurrentPeriodIssue> selectInventoryCurrentPeriodIssueByType(String document_type);
}
