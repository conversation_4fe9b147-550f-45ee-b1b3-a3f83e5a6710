package com.lucy.inventoryexit.service;

import java.util.List;

import com.lucy.assetowner.domain.AssetOwner;
import com.lucy.inventoryexit.domain.InventoryCurrentPeriodIssue;

/**
 * 本期出库明细 Current period inventory issuesService接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface IInventoryCurrentPeriodIssueService 
{
    /**
     * 查询本期出库明细 Current period inventory issues
     * 
     * @param id 本期出库明细 Current period inventory issues主键
     * @return 本期出库明细 Current period inventory issues
     */
    public InventoryCurrentPeriodIssue selectInventoryCurrentPeriodIssueById(Long id);

    /**
     * 查询本期出库明细 Current period inventory issues列表
     * 
     * @param inventoryCurrentPeriodIssue 本期出库明细 Current period inventory issues
     * @return 本期出库明细 Current period inventory issues集合
     */
    public List<InventoryCurrentPeriodIssue> selectInventoryCurrentPeriodIssueList(InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue);

    /**
     * 新增本期出库明细 Current period inventory issues
     *
     * @param inventoryCurrentPeriodIssue 本期出库明细 Current period inventory issues
     * @return 结果
     */
    public int insertInventoryCurrentPeriodIssue(InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue);

    /**
     * 批量新增本期出库明细 Current period inventory issues
     *
     * @param inventoryCurrentPeriodIssues 本期出库明细 Current period inventory issues列表
     * @return 结果
     */
    public int batchInsertInventoryCurrentPeriodIssue(List<InventoryCurrentPeriodIssue> inventoryCurrentPeriodIssues);

    /**
     * 修改本期出库明细 Current period inventory issues
     * 
     * @param inventoryCurrentPeriodIssue 本期出库明细 Current period inventory issues
     * @return 结果
     */
    public int updateInventoryCurrentPeriodIssue(InventoryCurrentPeriodIssue inventoryCurrentPeriodIssue);

    /**
     * 批量删除本期出库明细 Current period inventory issues
     * 
     * @param ids 需要删除的本期出库明细 Current period inventory issues主键集合
     * @return 结果
     */
    public int deleteInventoryCurrentPeriodIssueByIds(Long[] ids);

    /**
     * 删除本期出库明细 Current period inventory issues信息
     * 
     * @param id 本期出库明细 Current period inventory issues主键
     * @return 结果
     */
    public int deleteInventoryCurrentPeriodIssueById(Long id);

    void loadBQCK(String dateStart, String dateEnd, AssetOwner assetOwner);

    List<InventoryCurrentPeriodIssue> loadXSCK(String startDate, String endDate, AssetOwner assetOwner, String month);

    List<InventoryCurrentPeriodIssue> loadCGTH(String startDate, String endDate, AssetOwner assetOwner, String month);

    List<InventoryCurrentPeriodIssue> loadYFSCLY(String startDate, String endDate, AssetOwner assetOwner, String month);

    List<InventoryCurrentPeriodIssue> loadHCLY(String startDate, String endDate, AssetOwner assetOwner, String month);

    List<InventoryCurrentPeriodIssue> loadCCDTZ(String startDate, String endDate, AssetOwner assetOwner, String month);

    List<InventoryCurrentPeriodIssue> loadBF(String startDate, String endDate, AssetOwner assetOwner, String month);

    List<InventoryCurrentPeriodIssue> loadPK(String startDate, String endDate, AssetOwner assetOwner, String month);

    List<InventoryCurrentPeriodIssue> loadCB(String startDate, String endDate, AssetOwner assetOwner, String month);

    List<InventoryCurrentPeriodIssue> loadFZ(String startDate, String endDate, AssetOwner assetOwner, String month);

    List<InventoryCurrentPeriodIssue> loadXYSC(String startDate, String endDate, AssetOwner assetOwner, String month);

    List<InventoryCurrentPeriodIssue> loadHHH(String startDate, String endDate, AssetOwner assetOwner, String month);

    List<InventoryCurrentPeriodIssue> selectInventoryCurrentPeriodIssueByType(String document_type);

    void loadConversionData(String startDate, String endDate, AssetOwner assetOwner);

    void loadDependencyGraph(String assetOwner);

    void updateCost(String month, String assetOwner);
}
