package com.lucy.assetowner.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lucy.assetowner.mapper.AssetOwnerMasterMapper;
import com.lucy.assetowner.domain.AssetOwnerMaster;
import com.lucy.assetowner.service.IAssetOwnerMasterService;

/**
 * 资产所属主体主 Asset owner master dataService业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-04
 */
@Service
public class AssetOwnerMasterServiceImpl implements IAssetOwnerMasterService 
{
    @Autowired
    private AssetOwnerMasterMapper assetOwnerMasterMapper;

    //缓存数据

    private static Map<String, AssetOwnerMaster> registry = new HashMap<>();
    /**
     * 查询资产所属主体主 Asset owner master data
     * 
     * @param id 资产所属主体主 Asset owner master data主键
     * @return 资产所属主体主 Asset owner master data
     */
    @Override
    public AssetOwnerMaster selectAssetOwnerMasterById(Long id)
    {
        return assetOwnerMasterMapper.selectAssetOwnerMasterById(id);
    }

    /**
     * 查询资产所属主体主 Asset owner master data列表
     * 
     * @param assetOwnerMaster 资产所属主体主 Asset owner master data
     * @return 资产所属主体主 Asset owner master data
     */
    @Override
    public List<AssetOwnerMaster> selectAssetOwnerMasterList(AssetOwnerMaster assetOwnerMaster)
    {
        return assetOwnerMasterMapper.selectAssetOwnerMasterList(assetOwnerMaster);
    }

    /**
     * 新增资产所属主体主 Asset owner master data
     * 
     * @param assetOwnerMaster 资产所属主体主 Asset owner master data
     * @return 结果
     */
    @Override
    public int insertAssetOwnerMaster(AssetOwnerMaster assetOwnerMaster)
    {
        return assetOwnerMasterMapper.insertAssetOwnerMaster(assetOwnerMaster);
    }

    /**
     * 修改资产所属主体主 Asset owner master data
     * 
     * @param assetOwnerMaster 资产所属主体主 Asset owner master data
     * @return 结果
     */
    @Override
    public int updateAssetOwnerMaster(AssetOwnerMaster assetOwnerMaster)
    {
        return assetOwnerMasterMapper.updateAssetOwnerMaster(assetOwnerMaster);
    }

    /**
     * 批量删除资产所属主体主 Asset owner master data
     * 
     * @param ids 需要删除的资产所属主体主 Asset owner master data主键
     * @return 结果
     */
    @Override
    public int deleteAssetOwnerMasterByIds(Long[] ids)
    {
        return assetOwnerMasterMapper.deleteAssetOwnerMasterByIds(ids);
    }

    /**
     * 删除资产所属主体主 Asset owner master data信息
     * 
     * @param id 资产所属主体主 Asset owner master data主键
     * @return 结果
     */
    @Override
    public int deleteAssetOwnerMasterById(Long id)
    {
        return assetOwnerMasterMapper.deleteAssetOwnerMasterById(id);
    }

    @Override
    public void loadData() {
        List<AssetOwnerMaster>  list = assetOwnerMasterMapper.selectAssetOwnerMasterList( new AssetOwnerMaster());
        registry = list.stream().collect(Collectors.toMap(AssetOwnerMaster::getCode, Function.identity()));

        System.out.println("AssetOwnerMaster 数据已加载，共 " + (list != null ? list.size() : 0) + " 条记录");
    }

    @Override
    public AssetOwnerMaster get(String code) {
        return registry.get(code);
    }

    @Override
    public List<AssetOwnerMaster> selectAssetOwnerMaster() {
        return assetOwnerMasterMapper.selectAssetOwnerMaster();
    }
}
