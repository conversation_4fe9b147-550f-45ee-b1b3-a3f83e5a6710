<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lucy.assetowner.mapper.AssetOwnerMasterMapper">
    
    <resultMap type="AssetOwnerMaster" id="AssetOwnerMasterResult">
        <result property="id"    column="id"    />
        <result property="code"    column="code"    />
        <result property="name"    column="name"    />
        <result property="shortName"    column="short_name"    />
        <result property="type"    column="type"    />
        <result property="parentCode"    column="parent_code"    />
        <result property="level"    column="level"    />
        <result property="path"    column="path"    />
        <result property="costCenter"    column="cost_center"    />
        <result property="profitCenter"    column="profit_center"    />
        <result property="taxNumber"    column="tax_number"    />
        <result property="status"    column="status"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="updatedTime"    column="updated_time"    />
        <result property="version"    column="version"    />
        <result property="isDeleted"    column="is_deleted"    />
    </resultMap>

    <sql id="selectAssetOwnerMasterVo">
        select id, code, name, short_name, type, parent_code, level, path, cost_center, profit_center, tax_number, status, start_date, end_date, created_by, created_time, updated_by, updated_time, version, is_deleted from asset_owner_master
    </sql>

    <select id="selectAssetOwnerMasterList" parameterType="AssetOwnerMaster" resultMap="AssetOwnerMasterResult">
        <include refid="selectAssetOwnerMasterVo"/>
        <where>  
            <if test="code != null  and code != ''"> and code = #{code}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="shortName != null  and shortName != ''"> and short_name like concat('%', #{shortName}, '%')</if>
            <if test="type != null  and type != ''"> and type = #{type}</if>
            <if test="parentCode != null  and parentCode != ''"> and parent_code = #{parentCode}</if>
            <if test="level != null "> and level = #{level}</if>
            <if test="path != null  and path != ''"> and path = #{path}</if>
            <if test="costCenter != null  and costCenter != ''"> and cost_center = #{costCenter}</if>
            <if test="profitCenter != null  and profitCenter != ''"> and profit_center = #{profitCenter}</if>
            <if test="taxNumber != null  and taxNumber != ''"> and tax_number = #{taxNumber}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="startDate != null "> and start_date = #{startDate}</if>
            <if test="endDate != null "> and end_date = #{endDate}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="updatedBy != null  and updatedBy != ''"> and updated_by = #{updatedBy}</if>
            <if test="updatedTime != null "> and updated_time = #{updatedTime}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
        </where>
    </select>
    
    <select id="selectAssetOwnerMasterById" parameterType="Long" resultMap="AssetOwnerMasterResult">
        <include refid="selectAssetOwnerMasterVo"/>
        where id = #{id}
    </select>

    <insert id="insertAssetOwnerMaster" parameterType="AssetOwnerMaster" useGeneratedKeys="true" keyProperty="id">
        insert into asset_owner_master
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="shortName != null">short_name,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="parentCode != null">parent_code,</if>
            <if test="level != null">level,</if>
            <if test="path != null">path,</if>
            <if test="costCenter != null">cost_center,</if>
            <if test="profitCenter != null">profit_center,</if>
            <if test="taxNumber != null">tax_number,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="createdBy != null and createdBy != ''">created_by,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedBy != null and updatedBy != ''">updated_by,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="version != null">version,</if>
            <if test="isDeleted != null">is_deleted,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="shortName != null">#{shortName},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="parentCode != null">#{parentCode},</if>
            <if test="level != null">#{level},</if>
            <if test="path != null">#{path},</if>
            <if test="costCenter != null">#{costCenter},</if>
            <if test="profitCenter != null">#{profitCenter},</if>
            <if test="taxNumber != null">#{taxNumber},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="createdBy != null and createdBy != ''">#{createdBy},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedBy != null and updatedBy != ''">#{updatedBy},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="version != null">#{version},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
         </trim>
    </insert>

    <update id="updateAssetOwnerMaster" parameterType="AssetOwnerMaster">
        update asset_owner_master
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="shortName != null">short_name = #{shortName},</if>
            <if test="type != null and type != ''">type = #{type},</if>
            <if test="parentCode != null">parent_code = #{parentCode},</if>
            <if test="level != null">level = #{level},</if>
            <if test="path != null">path = #{path},</if>
            <if test="costCenter != null">cost_center = #{costCenter},</if>
            <if test="profitCenter != null">profit_center = #{profitCenter},</if>
            <if test="taxNumber != null">tax_number = #{taxNumber},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="startDate != null">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="createdBy != null and createdBy != ''">created_by = #{createdBy},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedBy != null and updatedBy != ''">updated_by = #{updatedBy},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="version != null">version = #{version},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAssetOwnerMasterById" parameterType="Long">
        delete from asset_owner_master where id = #{id}
    </delete>

    <delete id="deleteAssetOwnerMasterByIds" parameterType="String">
        delete from asset_owner_master where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAssetOwnerMaster" resultMap="AssetOwnerMasterResult">
        <include refid="selectAssetOwnerMasterVo"/>
    </select>
</mapper>