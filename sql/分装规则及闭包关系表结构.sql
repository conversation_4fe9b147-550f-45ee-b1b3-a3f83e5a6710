-- 1. 分装规则主表
CREATE TABLE conversion_rule (
    rule_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '规则唯一标识，示例值: 101',
    rule_code VARCHAR(128) NOT NULL UNIQUE COMMENT '规则编码（可读标识），示例值: "DIL-ETH-95-TO-75"',
    description VARCHAR(255) NOT NULL COMMENT '规则描述，示例值: "95%乙醇稀释至75%"',
    conversion_type ENUM('DILUTION', 'MIX', 'PURIFICATION') NOT NULL COMMENT '转换类型：分装/DILUTION、混合/MIX、纯化/PURIFICATION，示例值: "DILUTION"',
    loss_rate DECIMAL(5,4) NOT NULL DEFAULT 0.0000 COMMENT '全局损耗率（0-1），示例值: 0.02 (2%损耗)',
    cost_method ENUM('QTY', 'EFFECTIVE_COMPONENT') NOT NULL COMMENT '成本分配方法：按数量/QTY、按有效成分/EFFECTIVE_COMPONENT，示例值: "EFFECTIVE_COMPONENT"',
    is_active BOOLEAN NOT NULL DEFAULT 1 COMMENT '是否启用，示例值: 1 (启用)',
    cycle_flag BOOLEAN NOT NULL DEFAULT 0 COMMENT '是否被检测到环状依赖，示例值: 0 (无环)',
     effective_date DATE NOT NULL COMMENT '规则生效日期',
    obsolete_date DATE NULL COMMENT '规则失效日期',
     VERSION SMALLINT NOT NULL DEFAULT 1 COMMENT '版本号',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，示例值: 2025-06-25 10:00:00',
    updated_at TIMESTAMP NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY unq_rule_version (rule_code, VERSION)
) COMMENT='分装规则主表';

-- 2. 分装规则明细表
CREATE TABLE conversion_rule_detail (
    detail_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '明细ID，示例值: 1001',
    rule_id INT NOT NULL COMMENT '关联conversion_rule.rule_id，示例值: 101',
    material_sku VARCHAR(50) NOT NULL COMMENT '物料SKU，示例值: "RAW-ETH-95%"',
    role ENUM('INPUT', 'OUTPUT') NOT NULL COMMENT '物料角色：输入/INPUT、输出/OUTPUT，示例值: "INPUT"',
    quantity DECIMAL(12,4) NOT NULL COMMENT '数量（输入为负，输出为正），示例值: -1000.0000 (输入)',
    uom VARCHAR(10) NOT NULL COMMENT '单位，示例值: "ml"',
    -- 化学特性字段
    concentration DECIMAL(5,2) NULL COMMENT '浓度百分比（仅纯物质），示例值: 95.00',
    effective_component VARCHAR(50) NULL COMMENT '有效成分名称（用于成本计算），示例值: "Ethanol"',
        component_ratio DECIMAL(5,4) GENERATED ALWAYS AS (
        CASE WHEN concentration IS NOT NULL
             THEN concentration / 100
             ELSE NULL
        END
    ) STORED COMMENT '有效成分占比（0-1），示例值: 0.95',

     -- 约束
    CHECK (
        (role = 'INPUT' AND quantity < 0) OR
        (role IN ('OUTPUT','CATALYST') AND quantity > 0)
    ),
    FOREIGN KEY (rule_id) REFERENCES conversion_rule(rule_id) ON DELETE CASCADE
) COMMENT='分装规则明细表';

-- 3. 物料依赖闭包表（优化结构）
CREATE TABLE conversion_material_dependency_closure (
    ancestor_sku VARCHAR(50) NOT NULL COMMENT '祖先节点SKU（起始物料）',
    descendant_sku VARCHAR(50) NOT NULL COMMENT '后代节点SKU（最终产物）',

    min_depth SMALLINT NOT NULL COMMENT '最短路径深度',
    max_depth SMALLINT NOT NULL COMMENT '最长路径深度',
    path_count SMALLINT NOT NULL DEFAULT 1 COMMENT '路径数量',
    PRIMARY KEY (ancestor_sku, descendant_sku),
    INDEX idx_descendant (descendant_sku),
    INDEX idx_depth (min_depth, max_depth)
) COMMENT='物料依赖图谱表';

-- 4. 环状依赖记录表
CREATE TABLE conversion_dependency_cycle (
    cycle_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '环ID',
    cycle_path VARCHAR(500) NOT NULL UNIQUE COMMENT '环路径（SKU链，如"A>B>C>A"）',
    material_list JSON NOT NULL COMMENT '涉及物料清单（用于锁定）',
     STATUS ENUM('DETECTED','RESOLVED','EXCEPTION') NOT NULL DEFAULT 'DETECTED' COMMENT '处理状态',
    resolved_method VARCHAR(50) NULL COMMENT '解决方法',
    detected_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '检测时间',
      resolved_at TIMESTAMP NULL COMMENT '解决时间'
) COMMENT='环状依赖记录表';


-- 5. 分装成本卷积表
CREATE TABLE conversion_cost_convolution (
    convolution_id INT AUTO_INCREMENT PRIMARY KEY COMMENT '卷积ID',
     month_key CHAR(6) NOT NULL COMMENT '月份键YYYYMM',
    target_sku VARCHAR(50) NOT NULL COMMENT '目标物料SKU（最终产物）',
    depth SMALLINT NOT NULL COMMENT '当前计算深度（从最深开始）',
    rule_id INT NOT NULL COMMENT '当前层使用的分装规则',
    input_cost DECIMAL(12,4) NOT NULL DEFAULT 0.0000 COMMENT '输入物料总成本',
    processing_cost DECIMAL(12,4) NOT NULL DEFAULT 0.0000 COMMENT '加工费用（人工、设备）',
    output_cost DECIMAL(12,4) GENERATED ALWAYS AS ((input_cost + processing_cost)) STORED COMMENT '输出物料单位成本（自动计算）',
    calc_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '计算时间',
    INDEX idx_month_sku_depth (month_key, target_sku, depth),
    FOREIGN KEY (rule_id) REFERENCES conversion_rule(rule_id)
) COMMENT='分装成本卷积表';



-- 6. `分装事务表`
CREATE TABLE conversion_transaction (
    trans_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '事务ID',
    rule_id INT NOT NULL COMMENT '使用规则',
    input_sku VARCHAR(50) NOT NULL COMMENT '输入物料',
    output_sku VARCHAR(50) NOT NULL COMMENT '输出物料',
    input_quantity DECIMAL(12,4) NOT NULL COMMENT '输入数量',
    output_quantity DECIMAL(12,4) NOT NULL COMMENT '输出数量',
    actual_loss_rate DECIMAL(5,4) NOT NULL COMMENT '实际损耗率',
    transaction_date DATE NOT NULL COMMENT '事务日期',
    month_key CHAR(6) GENERATED ALWAYS AS (DATE_FORMAT(transaction_date, '%Y%m')) STORED COMMENT '月份键',
    trans_bizcd VARCHAR(50)NOT NULL COMMENT '事务表业务单号',
    cost_allocated BOOLEAN NOT NULL DEFAULT 0 COMMENT '成本已分配',
    FOREIGN KEY (rule_id) REFERENCES conversion_rule(rule_id),
    INDEX idx_trans_date (transaction_date),
    INDEX idx_month_sku (month_key, input_sku, output_sku)
) COMMENT='分装事务表';


-- 2025-07-22增量
ALTER TABLE `lucy_bridge_erp_u8`.`conversion_material_dependency_closure`
  CHANGE `min_depth` `min_depth` SMALLINT(6) NULL  COMMENT '最短路径深度',
  CHANGE `path_count` `path_count` SMALLINT(6) DEFAULT 1  NULL  COMMENT '路径数量';