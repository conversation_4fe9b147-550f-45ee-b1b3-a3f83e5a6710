package com.ruoyi.repackage.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.repackage.mapper.ConversionMaterialDependencyClosureMapper;
import com.ruoyi.repackage.domain.ConversionMaterialDependencyClosure;
import com.ruoyi.repackage.service.IConversionMaterialDependencyClosureService;

/**
 * 物料依赖图谱Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class ConversionMaterialDependencyClosureServiceImpl implements IConversionMaterialDependencyClosureService 
{
    @Autowired
    private ConversionMaterialDependencyClosureMapper conversionMaterialDependencyClosureMapper;

    /**
     * 查询物料依赖图谱
     * 
     * @param ancestorSku 物料依赖图谱主键
     * @return 物料依赖图谱
     */
    @Override
    public ConversionMaterialDependencyClosure selectConversionMaterialDependencyClosureByAncestorSku(String ancestorSku)
    {
        return conversionMaterialDependencyClosureMapper.selectConversionMaterialDependencyClosureByAncestorSku(ancestorSku);
    }

    /**
     * 查询物料依赖图谱列表
     * 
     * @param conversionMaterialDependencyClosure 物料依赖图谱
     * @return 物料依赖图谱
     */
    @Override
    public List<ConversionMaterialDependencyClosure> selectConversionMaterialDependencyClosureList(ConversionMaterialDependencyClosure conversionMaterialDependencyClosure)
    {
        return conversionMaterialDependencyClosureMapper.selectConversionMaterialDependencyClosureList(conversionMaterialDependencyClosure);
    }

    /**
     * 新增物料依赖图谱
     * 
     * @param conversionMaterialDependencyClosure 物料依赖图谱
     * @return 结果
     */
    @Override
    public int insertConversionMaterialDependencyClosure(ConversionMaterialDependencyClosure conversionMaterialDependencyClosure)
    {
        return conversionMaterialDependencyClosureMapper.insertConversionMaterialDependencyClosure(conversionMaterialDependencyClosure);
    }

    /**
     * 修改物料依赖图谱
     * 
     * @param conversionMaterialDependencyClosure 物料依赖图谱
     * @return 结果
     */
    @Override
    public int updateConversionMaterialDependencyClosure(ConversionMaterialDependencyClosure conversionMaterialDependencyClosure)
    {
        return conversionMaterialDependencyClosureMapper.updateConversionMaterialDependencyClosure(conversionMaterialDependencyClosure);
    }

    /**
     * 批量删除物料依赖图谱
     * 
     * @param ancestorSkus 需要删除的物料依赖图谱主键
     * @return 结果
     */
    @Override
    public int deleteConversionMaterialDependencyClosureByAncestorSkus(String[] ancestorSkus)
    {
        return conversionMaterialDependencyClosureMapper.deleteConversionMaterialDependencyClosureByAncestorSkus(ancestorSkus);
    }

    /**
     * 删除物料依赖图谱信息
     * 
     * @param ancestorSku 物料依赖图谱主键
     * @return 结果
     */
    @Override
    public int deleteConversionMaterialDependencyClosureByAncestorSku(String ancestorSku)
    {
        return conversionMaterialDependencyClosureMapper.deleteConversionMaterialDependencyClosureByAncestorSku(ancestorSku);
    }

    @Override
    public ConversionMaterialDependencyClosure selectConversionMaterialDependencyClosureBySku(String ancestorSku, String descendantSku) {
        return conversionMaterialDependencyClosureMapper.selectConversionMaterialDependencyClosureBySku(ancestorSku, descendantSku);
    }

    @Override
    public List<ConversionMaterialDependencyClosure> selectConversionMaterialDependencyClosureByDescendantSku(Set<String> descendantSku) {
        return conversionMaterialDependencyClosureMapper.selectConversionMaterialDependencyClosureByDescendantSku(descendantSku);
    }

    @Override
    public List<ConversionMaterialDependencyClosure> selectConversionMaterialDependencyClosureAll() {
        return conversionMaterialDependencyClosureMapper.selectConversionMaterialDependencyClosureAll();
    }

    /**
     * 计算每个SKU作为后代节点时的最大深度
     * @param dependencies
     * @return Map<SKU, 最大深度>
     */
    @Override
    public LinkedHashMap<String, Integer> calculateMaxDescendantDepths(List<ConversionMaterialDependencyClosure> dependencies) {
        // 1. 过滤掉maxDepth为0的记录 ❌
        // 2. 按descendant_sku分组，计算每组的最大深度
        Map<String, Integer> depthMap = dependencies.stream()
//                .filter(d -> d.getMaxDepth() > 0)
                .collect(Collectors.toMap(
                        ConversionMaterialDependencyClosure::getDescendantSku,
                        ConversionMaterialDependencyClosure::getMaxDepth,
                        Integer::max  // 保留最大值
                ));
        // 3.按深度值升序排序
        return depthMap.entrySet().stream()
                .sorted(Map.Entry.comparingByValue())
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
    }

}
