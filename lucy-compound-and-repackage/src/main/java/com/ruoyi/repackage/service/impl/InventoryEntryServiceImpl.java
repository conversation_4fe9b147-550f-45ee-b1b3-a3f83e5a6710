package com.ruoyi.repackage.service.impl;

import com.ruoyi.repackage.domain.InventoryEntry;
import com.ruoyi.repackage.mapper.InventoryEntryMapper;
import com.ruoyi.repackage.service.IInventoryEntryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 本期入库明细 Current period inventory entriesService业务层处理
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
@Service
public class InventoryEntryServiceImpl implements IInventoryEntryService
{
    private static final Logger log = LoggerFactory.getLogger(InventoryEntryServiceImpl.class);

    private static final String TABLE_NAME = "inventory_current_period_entry";

    // 批量插入的批次大小，避免内存溢出

    private static final int BATCH_SIZE = 1000;

    private List<String> dj_list = Arrays.asList("销售退货","产品分装入库-小样生产","包材入库","产品分装入库-现货","产品分装入库-非现货","换货号生产入","换货号入");

    @Autowired
    private InventoryEntryMapper inventoryEntryMapper;

    // 数据处理专用线程池，用于并行处理
    private final ExecutorService dataProcessorExecutor = Executors.newFixedThreadPool(
            Math.max(4, Runtime.getRuntime().availableProcessors())
    );

    /**
     * 查询本期入库明细 Current period inventory entries
     * 
     * @param id 本期入库明细 Current period inventory entries主键
     * @return 本期入库明细 Current period inventory entries
     */
    @Override
    public InventoryEntry selectInventoryCurrentPeriodEntryById(Long id)
    {
        return inventoryEntryMapper.selectInventoryCurrentPeriodEntryById(id);
    }

    /**
     * 查询本期入库明细 Current period inventory entries列表
     * 
     * @param inventoryEntry 本期入库明细 Current period inventory entries
     * @return 本期入库明细 Current period inventory entries
     */
    @Override
    public List<InventoryEntry> selectInventoryCurrentPeriodEntryList(InventoryEntry inventoryEntry)
    {
        return inventoryEntryMapper.selectInventoryCurrentPeriodEntryList(inventoryEntry);
    }

    /**
     *
     * @param inventoryEntries
     * @return
     */
    @Override
    public int batchInsertInventoryCurrentPeriodEntry(List<InventoryEntry> inventoryEntries) {
        if (inventoryEntries == null || inventoryEntries.isEmpty()) {
            return 0;
        }

        int totalInserted = 0;
        int size = inventoryEntries.size();

        // 分批处理，避免内存溢出和SQL语句过长
        for (int i = 0; i < size; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, size);
            List<InventoryEntry> batch = inventoryEntries.subList(i, endIndex);

            try {
                int inserted = inventoryEntryMapper.batchInsertInventoryCurrentPeriodEntry(batch);
                totalInserted += inserted;
            } catch (Exception e) {
                throw new RuntimeException("批量插入数据时发生异常" + e.getMessage(), e);
            }
        }

        return totalInserted;
    }

    /**
     * 新增本期入库明细 Current period inventory entries
     * 
     * @param inventoryEntry 本期入库明细 Current period inventory entries
     * @return 结果
     */
    @Override
    public int insertInventoryCurrentPeriodEntry(InventoryEntry inventoryEntry)
    {
        return inventoryEntryMapper.insertInventoryCurrentPeriodEntry(inventoryEntry);
    }

    /**
     * 修改本期入库明细 Current period inventory entries
     * 
     * @param inventoryEntry 本期入库明细 Current period inventory entries
     * @return 结果
     */
    @Override
    public int updateInventoryCurrentPeriodEntry(InventoryEntry inventoryEntry)
    {
        return inventoryEntryMapper.updateInventoryCurrentPeriodEntry(inventoryEntry);
    }

    /**
     * 批量删除本期入库明细 Current period inventory entries
     * 
     * @param ids 需要删除的本期入库明细 Current period inventory entries主键
     * @return 结果
     */
    @Override
    public int deleteInventoryCurrentPeriodEntryByIds(Long[] ids)
    {
        return inventoryEntryMapper.deleteInventoryCurrentPeriodEntryByIds(ids);
    }

    /**
     * 删除本期入库明细 Current period inventory entries信息
     * 
     * @param id 本期入库明细 Current period inventory entries主键
     * @return 结果
     */
    @Override
    public int deleteInventoryCurrentPeriodEntryById(Long id)
    {
        return inventoryEntryMapper.deleteInventoryCurrentPeriodEntryById(id);
    }

    @Override
    public List<InventoryEntry> selectInventoryCurrentPeriodEntryBySkuAndPeriod(InventoryEntry inventoryEntry) {
        return inventoryEntryMapper.selectInventoryCurrentPeriodEntryBySkuAndPeriod(inventoryEntry);
    }
}
