package com.ruoyi.repackage.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.ruoyi.repackage.domain.ConversionRule;
import com.ruoyi.repackage.service.IConversionRuleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.repackage.mapper.ConversionTransactionMapper;
import com.ruoyi.repackage.domain.ConversionTransaction;
import com.ruoyi.repackage.service.IConversionTransactionService;

/**
 * 分装事务Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class ConversionTransactionServiceImpl implements IConversionTransactionService 
{
    @Autowired
    private ConversionTransactionMapper conversionTransactionMapper;
    @Autowired
    private IConversionRuleService iConversionRuleService;

    /**
     * 查询分装事务
     * 
     * @param transId 分装事务主键
     * @return 分装事务
     */
    @Override
    public ConversionTransaction selectConversionTransactionByTransId(Long transId)
    {
        return conversionTransactionMapper.selectConversionTransactionByTransId(transId);
    }

    /**
     * 查询分装事务列表
     * 
     * @param conversionTransaction 分装事务
     * @return 分装事务
     */
    @Override
    public List<ConversionTransaction> selectConversionTransactionList(ConversionTransaction conversionTransaction)
    {
        return conversionTransactionMapper.selectConversionTransactionList(conversionTransaction);
    }

    /**
     * 新增分装事务
     * 
     * @param conversionTransaction 分装事务
     * @return 结果
     */
    @Override
    public int insertConversionTransaction(ConversionTransaction conversionTransaction)
    {
        return conversionTransactionMapper.insertConversionTransaction(conversionTransaction);
    }

    /**
     * 修改分装事务
     * 
     * @param conversionTransaction 分装事务
     * @return 结果
     */
    @Override
    public int updateConversionTransaction(ConversionTransaction conversionTransaction)
    {
        return conversionTransactionMapper.updateConversionTransaction(conversionTransaction);
    }

    /**
     * 批量删除分装事务
     * 
     * @param transIds 需要删除的分装事务主键
     * @return 结果
     */
    @Override
    public int deleteConversionTransactionByTransIds(Long[] transIds)
    {
        return conversionTransactionMapper.deleteConversionTransactionByTransIds(transIds);
    }

    /**
     * 删除分装事务信息
     * 
     * @param transId 分装事务主键
     * @return 结果
     */
    @Override
    public int deleteConversionTransactionByTransId(Long transId)
    {
        return conversionTransactionMapper.deleteConversionTransactionByTransId(transId);
    }

    @Override
    public List<ConversionTransaction> selectConversionTransactionBySku(List<String> sku) {
        return conversionTransactionMapper.selectConversionTransactionBySku(sku);
    }

    @Override
    public void insertConversionTransaction(String rule,String inputSku, String outputSku, BigDecimal inputQty, BigDecimal outputQty, Date date,String id) {
        ConversionTransaction conversionTransaction = new ConversionTransaction();
        ConversionRule conversionRule = iConversionRuleService.selectConversionRuleByRuleCode(rule);
        if (conversionRule!=null && !conversionRule.getRuleCode().isEmpty()){
            conversionTransaction.setRuleId(conversionRule.getRuleId());
        }else {

        }
        conversionTransaction.setInputSku(inputSku);
        conversionTransaction.setOutputSku(outputSku);
        conversionTransaction.setInputQuantity(inputQty);
        conversionTransaction.setOutputQuantity(outputQty);
        conversionTransaction.setActualLossRate(BigDecimal.ZERO);
        conversionTransaction.setTransactionDate(date);
        conversionTransaction.setTransBizcd(id);
        conversionTransactionMapper.insertConversionTransaction(conversionTransaction);
    }
}
