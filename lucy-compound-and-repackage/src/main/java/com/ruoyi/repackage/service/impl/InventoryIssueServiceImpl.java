package com.ruoyi.repackage.service.impl;

import com.ruoyi.repackage.domain.InventoryIssue;
import com.ruoyi.repackage.mapper.InventoryIssueMapper;
import com.ruoyi.repackage.service.IInventoryIssueService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;

/**
 * 本期出库明细 Current period inventory issuesService业务层处理
 *
 * <AUTHOR>
 * @date 2025-06-03
 */
@Service
public class InventoryIssueServiceImpl implements IInventoryIssueService {
    private static final Logger log = LoggerFactory.getLogger(InventoryIssueServiceImpl.class);

    private static final String TABLE_NAME = "inventory_current_period_issue";

    // 批量插入的批次大小，避免内存溢出
    private static final int BATCH_SIZE = 1000;

    // 线程池用于并行处理load方法
    private final ExecutorService executorService = Executors.newFixedThreadPool(8);

    // 数据处理专用线程池，用于并行处理
    private final ExecutorService dataProcessorExecutor = Executors.newFixedThreadPool(
            Math.max(4, Runtime.getRuntime().availableProcessors())
    );

    private List<String> dj_list = Arrays.asList("销售出库","采购退货","耗材领用","盘亏","拆包出","分装领料出库","生产领用","分装领料退库","换货号出","换货号生产出");

    @Autowired
    private InventoryIssueMapper inventoryIssueMapper;
    /**
     * 查询本期出库明细 Current period inventory issues
     *
     * @param id 本期出库明细 Current period inventory issues主键
     * @return 本期出库明细 Current period inventory issues
     */
    @Override
    public InventoryIssue selectInventoryCurrentPeriodIssueById(Long id) {
        return inventoryIssueMapper.selectInventoryCurrentPeriodIssueById(id);
    }

    /**
     * 查询本期出库明细 Current period inventory issues列表
     *
     * @param inventoryIssue 本期出库明细 Current period inventory issues
     * @return 本期出库明细 Current period inventory issues
     */
    @Override
    public List<InventoryIssue> selectInventoryCurrentPeriodIssueList(InventoryIssue inventoryIssue) {
        return inventoryIssueMapper.selectInventoryCurrentPeriodIssueList(inventoryIssue);
    }

    /**
     * 新增本期出库明细 Current period inventory issues
     *
     * @param inventoryIssue 本期出库明细 Current period inventory issues
     * @return 结果
     */
    @Override
    public int insertInventoryCurrentPeriodIssue(InventoryIssue inventoryIssue) {
        return inventoryIssueMapper.insertInventoryCurrentPeriodIssue(inventoryIssue);
    }

    /**
     * 批量新增本期出库明细 Current period inventory issues
     * 支持大数据量分批处理，避免内存溢出
     *
     * @param inventoryIssues 本期出库明细 Current period inventory issues列表
     * @return 结果
     */
    @Override
    public int batchInsertInventoryCurrentPeriodIssue(List<InventoryIssue> inventoryIssues) {
        if (inventoryIssues == null || inventoryIssues.isEmpty()) {
            return 0;
        }

        int totalInserted = 0;
        int size = inventoryIssues.size();

        // 分批处理，避免内存溢出和SQL语句过长
        for (int i = 0; i < size; i += BATCH_SIZE) {
            int endIndex = Math.min(i + BATCH_SIZE, size);
            List<InventoryIssue> batch = inventoryIssues.subList(i, endIndex);

            try {
                int inserted = inventoryIssueMapper.batchInsertInventoryCurrentPeriodIssue(batch);
                totalInserted += inserted;
            } catch (Exception e) {
                throw new RuntimeException("批量插入数据时发生异常" + e.getMessage(), e);
            }
        }

        return totalInserted;
    }

    /**
     * 修改本期出库明细 Current period inventory issues
     *
     * @param inventoryIssue 本期出库明细 Current period inventory issues
     * @return 结果
     */
    @Override
    public int updateInventoryCurrentPeriodIssue(InventoryIssue inventoryIssue) {
        return inventoryIssueMapper.updateInventoryCurrentPeriodIssue(inventoryIssue);
    }

    /**
     * 批量删除本期出库明细 Current period inventory issues
     *
     * @param ids 需要删除的本期出库明细 Current period inventory issues主键
     * @return 结果
     */
    @Override
    public int deleteInventoryCurrentPeriodIssueByIds(Long[] ids) {
        return inventoryIssueMapper.deleteInventoryCurrentPeriodIssueByIds(ids);
    }

    /**
     * 删除本期出库明细 Current period inventory issues信息
     *
     * @param id 本期出库明细 Current period inventory issues主键
     * @return 结果
     */
    @Override
    public int deleteInventoryCurrentPeriodIssueById(Long id) {
        return inventoryIssueMapper.deleteInventoryCurrentPeriodIssueById(id);
    }

    @Override
    public List<InventoryIssue> selectInventoryCurrentPeriodIssueByType(InventoryIssue inventoryIssue) {
        return inventoryIssueMapper.selectInventoryCurrentPeriodIssueByType(inventoryIssue);
    }
}
