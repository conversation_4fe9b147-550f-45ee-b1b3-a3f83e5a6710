package com.ruoyi.repackage.service;

import java.util.List;
import com.ruoyi.repackage.domain.ConversionRule;

/**
 * 分装规则主Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IConversionRuleService 
{
    /**
     * 查询分装规则主
     * 
     * @param ruleId 分装规则主主键
     * @return 分装规则主
     */
    public ConversionRule selectConversionRuleByRuleId(Long ruleId);

    /**
     * 查询分装规则
     *
     * @param ruleCode 分装规则规则
     * @return 分装规则主
     */
    public ConversionRule selectConversionRuleByRuleCode(String ruleCode);

    /**
     * 查询分装规则主列表
     * 
     * @param conversionRule 分装规则主
     * @return 分装规则主集合
     */
    public List<ConversionRule> selectConversionRuleList(ConversionRule conversionRule);

    /**
     * 新增分装规则主
     * 
     * @param conversionRule 分装规则主
     * @return 结果
     */
    public Long insertConversionRule(ConversionRule conversionRule);

    /**
     * 修改分装规则主
     * 
     * @param conversionRule 分装规则主
     * @return 结果
     */
    public int updateConversionRule(ConversionRule conversionRule);

    /**
     * 批量删除分装规则主
     * 
     * @param ruleIds 需要删除的分装规则主主键集合
     * @return 结果
     */
    public int deleteConversionRuleByRuleIds(Long[] ruleIds);

    /**
     * 删除分装规则主信息
     * 
     * @param ruleId 分装规则主主键
     * @return 结果
     */
    public int deleteConversionRuleByRuleId(Long ruleId);

    public List<String> selectCycleSku();

    public Long getConversionRuleId(String ruleCode, String assetOwner);
}
