package com.ruoyi.repackage.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.repackage.mapper.ConversionDependencyCycleMapper;
import com.ruoyi.repackage.domain.ConversionDependencyCycle;
import com.ruoyi.repackage.service.IConversionDependencyCycleService;

/**
 * 环状依赖记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class ConversionDependencyCycleServiceImpl implements IConversionDependencyCycleService 
{
    @Autowired
    private ConversionDependencyCycleMapper conversionDependencyCycleMapper;

    /**
     * 查询环状依赖记录
     * 
     * @param cycleId 环状依赖记录主键
     * @return 环状依赖记录
     */
    @Override
    public ConversionDependencyCycle selectConversionDependencyCycleByCycleId(Long cycleId)
    {
        return conversionDependencyCycleMapper.selectConversionDependencyCycleByCycleId(cycleId);
    }

    /**
     * 查询环状依赖记录列表
     * 
     * @param conversionDependencyCycle 环状依赖记录
     * @return 环状依赖记录
     */
    @Override
    public List<ConversionDependencyCycle> selectConversionDependencyCycleList(ConversionDependencyCycle conversionDependencyCycle)
    {
        return conversionDependencyCycleMapper.selectConversionDependencyCycleList(conversionDependencyCycle);
    }

    /**
     * 新增环状依赖记录
     * 
     * @param conversionDependencyCycle 环状依赖记录
     * @return 结果
     */
    @Override
    public int insertConversionDependencyCycle(ConversionDependencyCycle conversionDependencyCycle)
    {
        return conversionDependencyCycleMapper.insertConversionDependencyCycle(conversionDependencyCycle);
    }

    /**
     * 修改环状依赖记录
     * 
     * @param conversionDependencyCycle 环状依赖记录
     * @return 结果
     */
    @Override
    public int updateConversionDependencyCycle(ConversionDependencyCycle conversionDependencyCycle)
    {
        return conversionDependencyCycleMapper.updateConversionDependencyCycle(conversionDependencyCycle);
    }

    /**
     * 批量删除环状依赖记录
     * 
     * @param cycleIds 需要删除的环状依赖记录主键
     * @return 结果
     */
    @Override
    public int deleteConversionDependencyCycleByCycleIds(Long[] cycleIds)
    {
        return conversionDependencyCycleMapper.deleteConversionDependencyCycleByCycleIds(cycleIds);
    }

    /**
     * 删除环状依赖记录信息
     * 
     * @param cycleId 环状依赖记录主键
     * @return 结果
     */
    @Override
    public int deleteConversionDependencyCycleByCycleId(Long cycleId)
    {
        return conversionDependencyCycleMapper.deleteConversionDependencyCycleByCycleId(cycleId);
    }

    @Override
    public List<ConversionDependencyCycle> selectConversionDependencyCycleAll() {
        return conversionDependencyCycleMapper.selectConversionDependencyCycleAll();
    }
}
