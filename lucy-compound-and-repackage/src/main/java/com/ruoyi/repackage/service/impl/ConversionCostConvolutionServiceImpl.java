package com.ruoyi.repackage.service.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lucy.assetowner.domain.AssetOwnerMaster;
import com.lucy.assetowner.service.IAssetOwnerMasterService;
import com.lucy.inventoryoe.domain.InventoryOpeningBalance;
import com.lucy.inventoryoe.service.IInventoryOpeningBalanceService;
import com.ruoyi.repackage.domain.*;
import com.ruoyi.repackage.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.repackage.mapper.ConversionCostConvolutionMapper;

/**
 * 分装成本卷积Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class ConversionCostConvolutionServiceImpl implements IConversionCostConvolutionService 
{
    @Autowired
    private ConversionCostConvolutionMapper conversionCostConvolutionMapper;
    @Autowired
    private IInventoryIssueService iInventoryIssueService;
    @Autowired
    private IConversionMaterialDependencyClosureService iConversionMaterialDependencyClosureService;
    @Autowired
    private IInventoryEntryService iInventoryEntryService;
    @Autowired
    private IInventoryOpeningBalanceService iInventoryOpeningBalanceService;
    @Autowired
    private IConversionCostConvolutionService iConversionCostConvolutionService;
    @Autowired
    private IConversionTransactionService iConversionTransactionService;
    @Autowired
    private IConversionDependencyCycleService iConversionDependencyCycleService;
    @Autowired
    private IConversionRuleService iConversionRuleService;
    @Autowired
    private IAssetOwnerMasterService iAssetOwnerMasterService;

    /**
     * 查询分装成本卷积
     * 
     * @param convolutionId 分装成本卷积主键
     * @return 分装成本卷积
     */
    @Override
    public ConversionCostConvolution selectConversionCostConvolutionByConvolutionId(Long convolutionId)
    {
        return conversionCostConvolutionMapper.selectConversionCostConvolutionByConvolutionId(convolutionId);
    }

    /**
     * 查询分装成本卷积列表
     * 
     * @param conversionCostConvolution 分装成本卷积
     * @return 分装成本卷积
     */
    @Override
    public List<ConversionCostConvolution> selectConversionCostConvolutionList(ConversionCostConvolution conversionCostConvolution)
    {
        return conversionCostConvolutionMapper.selectConversionCostConvolutionList(conversionCostConvolution);
    }

    /**
     * 新增分装成本卷积
     * 
     * @param conversionCostConvolution 分装成本卷积
     * @return 结果
     */
    @Override
    public int insertConversionCostConvolution(ConversionCostConvolution conversionCostConvolution)
    {
        return conversionCostConvolutionMapper.insertConversionCostConvolution(conversionCostConvolution);
    }

    /**
     * 修改分装成本卷积
     * 
     * @param conversionCostConvolution 分装成本卷积
     * @return 结果
     */
    @Override
    public int updateConversionCostConvolution(ConversionCostConvolution conversionCostConvolution)
    {
        return conversionCostConvolutionMapper.updateConversionCostConvolution(conversionCostConvolution);
    }

    /**
     * 批量删除分装成本卷积
     * 
     * @param convolutionIds 需要删除的分装成本卷积主键
     * @return 结果
     */
    @Override
    public int deleteConversionCostConvolutionByConvolutionIds(Long[] convolutionIds)
    {
        return conversionCostConvolutionMapper.deleteConversionCostConvolutionByConvolutionIds(convolutionIds);
    }

    /**
     * 删除分装成本卷积信息
     * 
     * @param convolutionId 分装成本卷积主键
     * @return 结果
     */
    @Override
    public int deleteConversionCostConvolutionByConvolutionId(Long convolutionId)
    {
        return conversionCostConvolutionMapper.deleteConversionCostConvolutionByConvolutionId(convolutionId);
    }

    /**
     * 计算当月平均成本
     *
     * @param yearMonth YYYY-MM type 01：第一次跑账 02：后续跑账
     * @return 结果
     */
    @Override
    public int calculateAverageCostMonth(String yearMonth ,String type) throws JsonProcessingException {
        List<AssetOwnerMaster> assetOwnerMasters = iAssetOwnerMasterService.selectAssetOwnerMaster();
        for (AssetOwnerMaster assetOwnerMaster : assetOwnerMasters) {
            //TODO 根据分装规则初始化当月的环状依赖表、闭包表

            String assetOwner = assetOwnerMaster.getCode();
            //查出需要计算成本的sku
            List<ConversionMaterialDependencyClosure> conversionMaterialDependencyClosures = new ArrayList<>();
            List<ConversionMaterialDependencyClosure> conversionMaterialDependencyClosuresAll = iConversionMaterialDependencyClosureService.selectConversionMaterialDependencyClosureAll();
            InventoryEntry inventoryEntry = new InventoryEntry();
            inventoryEntry.setAccountingPeriod(yearMonth);
            String yearmonth = yearMonth.replace("-", "");//YYYYMM
            if ("01".equals(type)){
                //第一次跑账 内部领用 document_type=耗材领用\生产领用\研发领用;
                List<InventoryIssue> inventoryIssueList = new ArrayList<>();
                InventoryIssue inventoryIssueParam = new InventoryIssue();
                inventoryIssueParam.setAssetOwner(assetOwner);
                inventoryIssueParam.setDocumentType("耗材领用");
                inventoryIssueList.addAll(iInventoryIssueService.selectInventoryCurrentPeriodIssueByType(inventoryIssueParam));
                inventoryIssueParam.setDocumentType("生产领用");
                inventoryIssueList.addAll(iInventoryIssueService.selectInventoryCurrentPeriodIssueByType(inventoryIssueParam));
                inventoryIssueParam.setDocumentType("研发领用");
                inventoryIssueList.addAll(iInventoryIssueService.selectInventoryCurrentPeriodIssueByType(inventoryIssueParam));
                List<String> skuList = inventoryIssueList.stream()
                        .map(InventoryIssue::getSku)
                        .distinct()
                        .collect(Collectors.toList());
                //根据skuList找到闭包表中跟这些sku相关的数据放在closureList中
                Set<String> collectedSkus = new HashSet<>();
                collectedSkus.addAll(skuList);
                for(String sku : skuList){
                    relevantSku(collectedSkus ,sku ,conversionMaterialDependencyClosuresAll);
                }
                if (!collectedSkus.isEmpty()) {
                    conversionMaterialDependencyClosures = iConversionMaterialDependencyClosureService.selectConversionMaterialDependencyClosureByDescendantSku(collectedSkus);
                }
            }else if ("02".equals(type)){
                //后续跑账
                conversionMaterialDependencyClosures = conversionMaterialDependencyClosuresAll;
            }

            //循环closureList 按照顺序给相关单据定价 从深度最深的开始定 0为最深
            Map<String, Integer> skuDepth = iConversionMaterialDependencyClosureService.calculateMaxDescendantDepths(conversionMaterialDependencyClosures);
            Set<String> skuSet = skuDepth.keySet();
            //查出依赖环的SKU
            List<String> cycleSku = iConversionRuleService.selectCycleSku();
            Map<String, Integer> cycleSkuDepth = new LinkedHashMap<>();
            List<InventoryOpeningBalance> inventoryOpeningBalances = iInventoryOpeningBalanceService.selectInventoryOpeningBalanceBySkuAndAccountingPeriod(skuSet ,yearMonth ,assetOwner);
            //本期入 产品生产入库、库存初始化、采购入库、盘盈
            inventoryEntry.setAssetOwner(assetOwner);
            List<InventoryEntry> inventoryCurrentPeriodEntries = iInventoryEntryService.selectInventoryCurrentPeriodEntryBySkuAndPeriod(inventoryEntry);
            //本期分装事务
            ConversionTransaction conversionTransaction = new ConversionTransaction();
            conversionTransaction.setMonthKey(yearmonth);
            conversionTransaction.setAssetOwner(assetOwner);
            List<ConversionTransaction> conversionTransactions = iConversionTransactionService.selectConversionTransactionList(conversionTransaction);
            //当月平均成本
            ConversionCostConvolution inputSkuCost = new ConversionCostConvolution();
            inputSkuCost.setMonthKey(yearmonth);
            inputSkuCost.setAssetOwner(assetOwner);
            List<ConversionCostConvolution> conversionCostConvolutions = conversionCostConvolutionMapper.selectConversionCostConvolutionList(inputSkuCost);
            skuDepth.forEach((sku, depth) -> {
                //如果是跟环有依赖的SKU 跳过
                if (cycleSku.contains(sku)) {
                    cycleSkuDepth.put(sku, depth);
                    return;
                }
                avgCostCurrentMonth(inventoryEntry, yearmonth, inventoryOpeningBalances, sku ,inventoryCurrentPeriodEntries ,conversionTransactions ,conversionCostConvolutions ,assetOwner);
            });

            //根据依赖环的SKU查出对应的环 依次定价 成环SKU>>依赖环的SKU
            ObjectMapper objectMapper = new ObjectMapper();
            Map<String, ConversionDependencyCycle> dependencyCycleMap = new HashMap();
            List<ConversionDependencyCycle> conversionDependencyCycles = iConversionDependencyCycleService.selectConversionDependencyCycleAll();
            //将成环的物料拆成 物料，环 存在map中
            for (ConversionDependencyCycle conversionDependencyCycle : conversionDependencyCycles) {
                List<String> list = objectMapper.readValue(
                        conversionDependencyCycle.getMaterialList(),
                        new TypeReference<List<String>>() {}
                );
                //键->物料 值->环
                list.forEach(item -> dependencyCycleMap.put(item, conversionDependencyCycle));
            }

            //计算跟环有依赖的物料以及成环的物料成本
            cycleSkuDepth.forEach((sku, depth) -> {
                List<ConversionTransaction> filteredConversionList = conversionTransactions.stream()
                        .filter(item -> sku.equals(item.getOutputSku()))
                        .collect(Collectors.toList());
                for (ConversionTransaction transaction: filteredConversionList) {
                    ConversionDependencyCycle conversionDependencyCycle = dependencyCycleMap.get(transaction.getInputSku());
                    if(conversionDependencyCycle == null){
                        continue;
                    }
                    //将成环的物料断开
                    List<String> skuStrings = avgCostCycleSku(conversionTransactions, conversionDependencyCycle, objectMapper);
                    //计算成环的物料成本
                    for (String cyclePathsSku: skuStrings) {
                        avgCostCurrentMonth(inventoryEntry, yearmonth, inventoryOpeningBalances, cyclePathsSku, inventoryCurrentPeriodEntries ,conversionTransactions ,conversionCostConvolutions ,assetOwner);
                    }
                }
                //计算依赖环的物料成本
                avgCostCurrentMonth(inventoryEntry, yearmonth, inventoryOpeningBalances, sku ,inventoryCurrentPeriodEntries ,conversionTransactions ,conversionCostConvolutions ,assetOwner);
            });

            //计算单独成环的物料成本
            dependencyCycleMap.forEach((sku, conversionDependencyCycle) -> {
                //将成环的物料断开
                List<String> skuStrings = avgCostCycleSku(conversionTransactions, conversionDependencyCycle, objectMapper);
                for (String cyclePathsSku: skuStrings) {
                    avgCostCurrentMonth(inventoryEntry, yearmonth, inventoryOpeningBalances, cyclePathsSku, inventoryCurrentPeriodEntries ,conversionTransactions ,conversionCostConvolutions ,assetOwner);
                }
            });

            //TODO 回刷相关单据

        }
        return 0;
    }

    /**
     * 将成环的物料断开
     * conversionTransactions 分装事务
     * conversionDependencyCycle 环
    * */
    private List<String> avgCostCycleSku(List<ConversionTransaction> conversionTransactions, ConversionDependencyCycle conversionDependencyCycle, ObjectMapper objectMapper) {
        List<String> skuStrings = new ArrayList<>();
        if ("RESOLVED".equals(conversionDependencyCycle.getStatus()) || "EXCEPTION".equals(conversionDependencyCycle.getStatus())) {
            return skuStrings;
        }
        try {
            List<String> list = objectMapper.readValue(
                    conversionDependencyCycle.getMaterialList(),
                    new TypeReference<List<String>>() {}
            );
            List<ConversionTransaction> transactionList = new ArrayList<>();
            for (String inputSku: list) {
                List<ConversionTransaction> collect = conversionTransactions.stream()
                        .filter(item -> inputSku.equals(item.getInputSku()))
                        .collect(Collectors.toList());
                transactionList.addAll(collect);
            }
            //找到最先发生的投料
            ConversionTransaction minConversionTransaction = transactionList.stream()
                    .min(Comparator.comparing(ConversionTransaction::getTransactionDate))
                    .orElse(null);
            //打断环依次计算成本
            skuStrings = splitCircularPath(conversionDependencyCycle.getCyclePath(), minConversionTransaction.getInputSku());
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
        conversionDependencyCycle.setStatus("RESOLVED");
        return skuStrings;
    }

    /**
     * 计算当月平均成本
     * inventoryCurrentPeriodEntry 本期发生入
     * yearmonth 年月YYYYMM
     * inventoryOpeningBalances 期初
     * sku
     * inventoryCurrentPeriodEntries 本期入 产品生产入库、库存初始化、采购入库、盘盈
     * conversionTransactions 本期分装事务
     * conversionCostConvolutions 当月平均成本
    * */
    private void avgCostCurrentMonth(InventoryEntry inventoryEntry, String yearmonth, List<InventoryOpeningBalance> inventoryOpeningBalances, String sku, List<InventoryEntry> inventoryCurrentPeriodEntries, List<ConversionTransaction> conversionTransactions, List<ConversionCostConvolution> conversionCostConvolutions, String assetOwner) {
        if (conversionCostConvolutions.stream()
                .filter(item -> sku.equals(item.getTargetSku()))
                .collect(Collectors.toList()).size() > 0) {
            // 成本已定价
            return;
        }

        //当月平均成本
        BigDecimal avgAmount = BigDecimal.ZERO;
        //期初数量
        BigDecimal openingQuantity = BigDecimal.ZERO;
        //期初金额
        BigDecimal openingAmount = BigDecimal.ZERO;
        //本期发生数量
        BigDecimal quantity = BigDecimal.ZERO;
        //本期发生金额
        BigDecimal amount = BigDecimal.ZERO;
        ConversionCostConvolution conversionCostConvolution = new ConversionCostConvolution();
        conversionCostConvolution.setMonthKey(yearmonth);

        //期初
        List<InventoryOpeningBalance> filteredList = inventoryOpeningBalances.stream()
                .filter(item -> sku.equals(item.getSku()))
                .collect(Collectors.toList());
        if(!filteredList.isEmpty()) {
            InventoryOpeningBalance inventoryOpeningBalance = filteredList.get(0);
            openingQuantity = inventoryOpeningBalance.getQuantity() == null ? BigDecimal.ZERO : inventoryOpeningBalance.getQuantity();
            openingAmount = inventoryOpeningBalance.getAmount() == null ? BigDecimal.ZERO : inventoryOpeningBalance.getAmount();
            //TODO 期初异常
            if (openingQuantity.compareTo(BigDecimal.ZERO) < 0) {

            } else if(openingAmount.compareTo(BigDecimal.ZERO) < 0) {

            }
        }

        //本期发生
        inventoryEntry.setSku(sku);
        //产品生产入库、库存初始化、采购入库、盘盈
        List<InventoryEntry> filteredEntryList = inventoryCurrentPeriodEntries.stream()
                .filter(item -> sku.equals(item.getSku()))
                .collect(Collectors.toList());
        for (InventoryEntry currentPeriodEntry : filteredEntryList) {
            quantity = quantity.add(currentPeriodEntry.getQuantity());
            amount = amount.add(currentPeriodEntry.getAmount());
        }
        //根据本期发生入库的sku找到所有本期发生出的sku 并算出所有出的sku总成本
        Map<String, BigDecimal> inputSku = new HashMap<>();
        List<ConversionTransaction> filteredTransactionsList = conversionTransactions.stream()
                .filter(item -> sku.equals(item.getOutputSku()))
                .collect(Collectors.toList());
        //相同输入sku数量累加
        for (ConversionTransaction transaction: filteredTransactionsList) {
            BigDecimal inputQuantity = transaction.getInputQuantity();
            if (inputSku.containsKey(transaction.getInputSku())) {
                inputQuantity = inputSku.get(transaction.getInputSku()).add(inputQuantity);
            }
            inputSku.put(transaction.getInputSku(), inputQuantity);
            quantity = quantity.add(transaction.getOutputQuantity());
        }
        //循环累加所有输入sku的成本
        AtomicReference<BigDecimal> atomicAmount = new AtomicReference<>(BigDecimal.ZERO);
        inputSku.forEach((inpSku, inpQty) -> {
            List<ConversionCostConvolution> filteredCostList = conversionCostConvolutions.stream()
                    .filter(item -> inpSku.equals(item.getTargetSku()))
                    .collect(Collectors.toList());
            if (!filteredCostList.isEmpty()) {
                //输入的投料当月平均成本*数量
                BigDecimal increment = filteredCostList.get(0)
                        .getInputCost()
                        .multiply(inpQty)
                        .setScale(4, BigDecimal.ROUND_HALF_UP);
                atomicAmount.updateAndGet(current -> current.add(increment));
            }
        });
        amount = amount.add(atomicAmount.get());

        //计算sku的当月平均成本
        avgAmount = (openingAmount.add(amount)).divide(openingQuantity.add(quantity) ,4, BigDecimal.ROUND_HALF_UP);
        conversionCostConvolution.setTargetSku(sku);
        conversionCostConvolution.setInputCost(avgAmount);
        conversionCostConvolution.setCalcTime(new Date());
        conversionCostConvolution.setAssetOwner(assetOwner);
        //插入数据
        iConversionCostConvolutionService.insertConversionCostConvolution(conversionCostConvolution);
        conversionCostConvolutions.add(conversionCostConvolution);
    }

    /**
     * 根据某个sku查出所有跟这个sku相关的sku
     *
    * */
    private Set<String> relevantSku(Set<String> collectedSkus ,String sku ,List<ConversionMaterialDependencyClosure> conversionMaterialDependencyClosuresAll) {
        List<ConversionMaterialDependencyClosure> conversionMaterialDependencyClosuresTemp = conversionMaterialDependencyClosuresAll.stream()
                .filter(item -> sku.equals(item.getDescendantSku()))
                .collect(Collectors.toList());
        for (ConversionMaterialDependencyClosure conversionMaterialDependencyClosure : conversionMaterialDependencyClosuresTemp) {
            if(!collectedSkus.contains(conversionMaterialDependencyClosure.getAncestorSku())) {
                collectedSkus.add(conversionMaterialDependencyClosure.getAncestorSku());
                relevantSku(collectedSkus, conversionMaterialDependencyClosure.getAncestorSku() ,conversionMaterialDependencyClosuresAll);
            }
        }
        return collectedSkus;
    }

    /**
    * path 环路径 A>B>C>A
     * startPoint 起始sku
    * */
    public static List<String> splitCircularPath(String path, String startPoint) {
        // Remove all '>' characters and split into nodes
        String[] nodes = path.replaceAll(">", "").split("");

        // Find the index of the starting point
        int startIndex = -1;
        for (int i = 0; i < nodes.length; i++) {
            if (nodes[i].equals(startPoint)) {
                startIndex = i;
                break;
            }
        }

        if (startIndex == -1) {
            throw new IllegalArgumentException("环状路径中未找到此物料！");
        }

        List<String> result = new ArrayList<>();
        for (int i = startIndex; i < nodes.length - 1; i++) {
            result.add(nodes[i]);
        }
        for (int i = 0; i < startIndex; i++) {
            result.add(nodes[i]);
        }

        return result;
    }
}
