package com.ruoyi.repackage.service.impl;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.repackage.mapper.ConversionRuleMapper;
import com.ruoyi.repackage.domain.ConversionRule;
import com.ruoyi.repackage.service.IConversionRuleService;

/**
 * 分装规则主Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@Service
public class ConversionRuleServiceImpl implements IConversionRuleService 
{
    @Autowired
    private ConversionRuleMapper conversionRuleMapper;

    /**
     * 查询分装规则主
     * 
     * @param ruleId 分装规则主主键
     * @return 分装规则主
     */
    @Override
    public ConversionRule selectConversionRuleByRuleId(Long ruleId)
    {
        return conversionRuleMapper.selectConversionRuleByRuleId(ruleId);
    }

    @Override
    public ConversionRule selectConversionRuleByRuleCode(String ruleCode) {
        return conversionRuleMapper.selectConversionRuleByRuleCode(ruleCode);
    }

    /**
     * 查询分装规则主列表
     * 
     * @param conversionRule 分装规则主
     * @return 分装规则主
     */
    @Override
    public List<ConversionRule> selectConversionRuleList(ConversionRule conversionRule)
    {
        return conversionRuleMapper.selectConversionRuleList(conversionRule);
    }

    /**
     * 新增分装规则主
     * 
     * @param conversionRule 分装规则主
     * @return 结果
     */
    @Override
    public Long insertConversionRule(ConversionRule conversionRule)
    {
        return conversionRuleMapper.insertConversionRule(conversionRule);
    }

    /**
     * 修改分装规则主
     * 
     * @param conversionRule 分装规则主
     * @return 结果
     */
    @Override
    public int updateConversionRule(ConversionRule conversionRule)
    {
        return conversionRuleMapper.updateConversionRule(conversionRule);
    }

    /**
     * 批量删除分装规则主
     * 
     * @param ruleIds 需要删除的分装规则主主键
     * @return 结果
     */
    @Override
    public int deleteConversionRuleByRuleIds(Long[] ruleIds)
    {
        return conversionRuleMapper.deleteConversionRuleByRuleIds(ruleIds);
    }

    /**
     * 删除分装规则主信息
     * 
     * @param ruleId 分装规则主主键
     * @return 结果
     */
    @Override
    public int deleteConversionRuleByRuleId(Long ruleId)
    {
        return conversionRuleMapper.deleteConversionRuleByRuleId(ruleId);
    }

    @Override
    public List<String> selectCycleSku() {
        return conversionRuleMapper.selectCycleSku();
    }

    /**
     * 获取分装规则ID
     * 如果规则不存在，则新增规则
     * @param ruleCode 分装规则代码
     * @return 分装规则ID
     */
    @Override
    public Long getConversionRuleId(String ruleCode, String assetOwner)
    {
        ConversionRule conversionRule = new ConversionRule();
        conversionRule.setRuleCode(ruleCode);
        conversionRule.setAssetOwner(assetOwner);
        List<ConversionRule> conversionRuleList = conversionRuleMapper.selectConversionRuleList(conversionRule);
        if (!conversionRuleList.isEmpty()) {
            return conversionRuleList.get(0).getRuleId();
        }
        conversionRule.setDescription("");
        // 分装
        conversionRule.setConversionType("DILUTION");
        conversionRule.setLossRate(BigDecimal.ZERO);
        conversionRule.setCostMethod("EFFECTIVE_COMPONENT");
        conversionRule.setEffectiveDate(new Date());
        conversionRuleMapper.insertConversionRule(conversionRule);
        return conversionRule.getRuleId();
    }
}
