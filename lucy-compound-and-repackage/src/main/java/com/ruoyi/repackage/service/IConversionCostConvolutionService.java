package com.ruoyi.repackage.service;

import java.util.List;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.repackage.domain.ConversionCostConvolution;

/**
 * 分装成本卷积Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IConversionCostConvolutionService 
{
    /**
     * 查询分装成本卷积
     * 
     * @param convolutionId 分装成本卷积主键
     * @return 分装成本卷积
     */
    public ConversionCostConvolution selectConversionCostConvolutionByConvolutionId(Long convolutionId);

    /**
     * 查询分装成本卷积列表
     * 
     * @param conversionCostConvolution 分装成本卷积
     * @return 分装成本卷积集合
     */
    public List<ConversionCostConvolution> selectConversionCostConvolutionList(ConversionCostConvolution conversionCostConvolution);

    /**
     * 新增分装成本卷积
     * 
     * @param conversionCostConvolution 分装成本卷积
     * @return 结果
     */
    public int insertConversionCostConvolution(ConversionCostConvolution conversionCostConvolution);

    /**
     * 修改分装成本卷积
     * 
     * @param conversionCostConvolution 分装成本卷积
     * @return 结果
     */
    public int updateConversionCostConvolution(ConversionCostConvolution conversionCostConvolution);

    /**
     * 批量删除分装成本卷积
     * 
     * @param convolutionIds 需要删除的分装成本卷积主键集合
     * @return 结果
     */
    public int deleteConversionCostConvolutionByConvolutionIds(Long[] convolutionIds);

    /**
     * 删除分装成本卷积信息
     * 
     * @param convolutionId 分装成本卷积主键
     * @return 结果
     */
    public int deleteConversionCostConvolutionByConvolutionId(Long convolutionId);

    /**
     * 计算当月平均成本
     *
     * @param yearMonth YYYY-MM type 01：第一次跑账 02：后续跑账
     * @return 结果
     */
    int calculateAverageCostMonth(String yearMonth ,String type) throws JsonProcessingException;;
}
