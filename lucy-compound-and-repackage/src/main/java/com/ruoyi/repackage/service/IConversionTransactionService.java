package com.ruoyi.repackage.service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.ruoyi.repackage.domain.ConversionTransaction;

/**
 * 分装事务Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IConversionTransactionService 
{
    /**
     * 查询分装事务
     * 
     * @param transId 分装事务主键
     * @return 分装事务
     */
    public ConversionTransaction selectConversionTransactionByTransId(Long transId);

    /**
     * 查询分装事务列表
     * 
     * @param conversionTransaction 分装事务
     * @return 分装事务集合
     */
    public List<ConversionTransaction> selectConversionTransactionList(ConversionTransaction conversionTransaction);

    /**
     * 新增分装事务
     * 
     * @param conversionTransaction 分装事务
     * @return 结果
     */
    public int insertConversionTransaction(ConversionTransaction conversionTransaction);

    /**
     * 修改分装事务
     * 
     * @param conversionTransaction 分装事务
     * @return 结果
     */
    public int updateConversionTransaction(ConversionTransaction conversionTransaction);

    /**
     * 批量删除分装事务
     * 
     * @param transIds 需要删除的分装事务主键集合
     * @return 结果
     */
    public int deleteConversionTransactionByTransIds(Long[] transIds);

    /**
     * 删除分装事务信息
     * 
     * @param transId 分装事务主键
     * @return 结果
     */
    public int deleteConversionTransactionByTransId(Long transId);

    public void insertConversionTransaction(String rule,String inputSku, String outputSku, BigDecimal inputQty, BigDecimal outputQty, Date date,String id);

    public List<ConversionTransaction> selectConversionTransactionBySku(List<String> sku);
}
