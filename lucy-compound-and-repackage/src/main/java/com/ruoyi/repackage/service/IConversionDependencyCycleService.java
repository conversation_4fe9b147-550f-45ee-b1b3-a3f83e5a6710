package com.ruoyi.repackage.service;

import java.util.List;
import com.ruoyi.repackage.domain.ConversionDependencyCycle;

/**
 * 环状依赖记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface IConversionDependencyCycleService 
{
    /**
     * 查询环状依赖记录
     * 
     * @param cycleId 环状依赖记录主键
     * @return 环状依赖记录
     */
    public ConversionDependencyCycle selectConversionDependencyCycleByCycleId(Long cycleId);

    /**
     * 查询环状依赖记录列表
     * 
     * @param conversionDependencyCycle 环状依赖记录
     * @return 环状依赖记录集合
     */
    public List<ConversionDependencyCycle> selectConversionDependencyCycleList(ConversionDependencyCycle conversionDependencyCycle);

    /**
     * 新增环状依赖记录
     * 
     * @param conversionDependencyCycle 环状依赖记录
     * @return 结果
     */
    public int insertConversionDependencyCycle(ConversionDependencyCycle conversionDependencyCycle);

    /**
     * 修改环状依赖记录
     * 
     * @param conversionDependencyCycle 环状依赖记录
     * @return 结果
     */
    public int updateConversionDependencyCycle(ConversionDependencyCycle conversionDependencyCycle);

    /**
     * 批量删除环状依赖记录
     * 
     * @param cycleIds 需要删除的环状依赖记录主键集合
     * @return 结果
     */
    public int deleteConversionDependencyCycleByCycleIds(Long[] cycleIds);

    /**
     * 删除环状依赖记录信息
     * 
     * @param cycleId 环状依赖记录主键
     * @return 结果
     */
    public int deleteConversionDependencyCycleByCycleId(Long cycleId);

    public List<ConversionDependencyCycle> selectConversionDependencyCycleAll();
}
