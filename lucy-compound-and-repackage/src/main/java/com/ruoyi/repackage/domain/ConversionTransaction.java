package com.ruoyi.repackage.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 分装事务对象 conversion_transaction
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public class ConversionTransaction extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 事务ID */
    private Long transId;

    /** 使用规则 */
    @Excel(name = "使用规则")
    private Long ruleId;

    /** 输入物料 */
    @Excel(name = "输入物料")
    private String inputSku;

    /** 输出物料 */
    @Excel(name = "输出物料")
    private String outputSku;

    /** 输入数量 */
    @Excel(name = "输入数量")
    private BigDecimal inputQuantity;

    /** 输出数量 */
    @Excel(name = "输出数量")
    private BigDecimal outputQuantity;

    /** 实际损耗率 */
    @Excel(name = "实际损耗率")
    private BigDecimal actualLossRate;

    /** 事务日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "事务日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date transactionDate;

    /** 月份键 */
    @Excel(name = "月份键")
    private String monthKey;

    /** 事务表业务单号 */
    @Excel(name = "事务表业务单号")
    private String transBizcd;

    /** 成本已分配 */
    @Excel(name = "成本已分配")
    private Integer costAllocated;

    /** 资产所属主体, Asset ownership entity */
    @Excel(name = "资产所属主体, Asset ownership entity")
    private String assetOwner;

    public String getAssetOwner() {
        return assetOwner;
    }

    public void setAssetOwner(String assetOwner) {
        this.assetOwner = assetOwner;
    }

    public void setTransId(Long transId)
    {
        this.transId = transId;
    }

    public Long getTransId() 
    {
        return transId;
    }

    public void setRuleId(Long ruleId) 
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId() 
    {
        return ruleId;
    }

    public void setInputSku(String inputSku) 
    {
        this.inputSku = inputSku;
    }

    public String getInputSku() 
    {
        return inputSku;
    }

    public void setOutputSku(String outputSku) 
    {
        this.outputSku = outputSku;
    }

    public String getOutputSku() 
    {
        return outputSku;
    }

    public void setInputQuantity(BigDecimal inputQuantity) 
    {
        this.inputQuantity = inputQuantity;
    }

    public BigDecimal getInputQuantity() 
    {
        return inputQuantity;
    }

    public void setOutputQuantity(BigDecimal outputQuantity) 
    {
        this.outputQuantity = outputQuantity;
    }

    public BigDecimal getOutputQuantity() 
    {
        return outputQuantity;
    }

    public void setActualLossRate(BigDecimal actualLossRate) 
    {
        this.actualLossRate = actualLossRate;
    }

    public BigDecimal getActualLossRate() 
    {
        return actualLossRate;
    }

    public void setTransactionDate(Date transactionDate) 
    {
        this.transactionDate = transactionDate;
    }

    public Date getTransactionDate() 
    {
        return transactionDate;
    }

    public void setMonthKey(String monthKey) 
    {
        this.monthKey = monthKey;
    }

    public String getMonthKey() 
    {
        return monthKey;
    }

    public void setTransBizcd(String transBizcd) 
    {
        this.transBizcd = transBizcd;
    }

    public String getTransBizcd() 
    {
        return transBizcd;
    }

    public void setCostAllocated(Integer costAllocated) 
    {
        this.costAllocated = costAllocated;
    }

    public Integer getCostAllocated() 
    {
        return costAllocated;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("transId", getTransId())
            .append("ruleId", getRuleId())
            .append("inputSku", getInputSku())
            .append("outputSku", getOutputSku())
            .append("inputQuantity", getInputQuantity())
            .append("outputQuantity", getOutputQuantity())
            .append("actualLossRate", getActualLossRate())
            .append("transactionDate", getTransactionDate())
            .append("monthKey", getMonthKey())
            .append("transBizcd", getTransBizcd())
            .append("costAllocated", getCostAllocated())
            .toString();
    }
}
