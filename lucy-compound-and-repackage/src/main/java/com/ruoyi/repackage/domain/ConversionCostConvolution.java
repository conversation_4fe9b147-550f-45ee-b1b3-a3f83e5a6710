package com.ruoyi.repackage.domain;

import java.math.BigDecimal;
import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 分装成本卷积对象 conversion_cost_convolution
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public class ConversionCostConvolution extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 卷积ID */
    private Long convolutionId;

    /** 月份键YYYYMM */
    @Excel(name = "月份键YYYYMM")
    private String monthKey;

    /** 目标物料SKU（最终产物） */
    @Excel(name = "目标物料SKU", readConverterExp = "最=终产物")
    private String targetSku;

    /** 当前计算深度（从最深开始） */
    @Excel(name = "当前计算深度", readConverterExp = "从=最深开始")
    private Integer depth;

    /** 当前层使用的分装规则 */
    @Excel(name = "当前层使用的分装规则")
    private Long ruleId;

    /** 输入物料总成本 */
    @Excel(name = "输入物料总成本")
    private BigDecimal inputCost;

    /** 加工费用（人工、设备） */
    @Excel(name = "加工费用", readConverterExp = "人=工、设备")
    private BigDecimal processingCost;

    /** 输出物料单位成本（自动计算） */
    @Excel(name = "输出物料单位成本", readConverterExp = "自=动计算")
    private BigDecimal outputCost;

    /** 计算时间 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "计算时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date calcTime;

    /** 资产所属主体, Asset ownership entity */
    @Excel(name = "资产所属主体, Asset ownership entity")
    private String assetOwner;

    public String getAssetOwner() {
        return assetOwner;
    }

    public void setAssetOwner(String assetOwner) {
        this.assetOwner = assetOwner;
    }

    public void setConvolutionId(Long convolutionId)
    {
        this.convolutionId = convolutionId;
    }

    public Long getConvolutionId() 
    {
        return convolutionId;
    }

    public void setMonthKey(String monthKey) 
    {
        this.monthKey = monthKey;
    }

    public String getMonthKey() 
    {
        return monthKey;
    }

    public void setTargetSku(String targetSku) 
    {
        this.targetSku = targetSku;
    }

    public String getTargetSku() 
    {
        return targetSku;
    }

    public void setDepth(Integer depth) 
    {
        this.depth = depth;
    }

    public Integer getDepth() 
    {
        return depth;
    }

    public void setRuleId(Long ruleId) 
    {
        this.ruleId = ruleId;
    }

    public Long getRuleId() 
    {
        return ruleId;
    }

    public void setInputCost(BigDecimal inputCost) 
    {
        this.inputCost = inputCost;
    }

    public BigDecimal getInputCost() 
    {
        return inputCost;
    }

    public void setProcessingCost(BigDecimal processingCost) 
    {
        this.processingCost = processingCost;
    }

    public BigDecimal getProcessingCost() 
    {
        return processingCost;
    }

    public void setOutputCost(BigDecimal outputCost) 
    {
        this.outputCost = outputCost;
    }

    public BigDecimal getOutputCost() 
    {
        return outputCost;
    }

    public void setCalcTime(Date calcTime) 
    {
        this.calcTime = calcTime;
    }

    public Date getCalcTime() 
    {
        return calcTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("convolutionId", getConvolutionId())
            .append("monthKey", getMonthKey())
            .append("targetSku", getTargetSku())
            .append("depth", getDepth())
            .append("ruleId", getRuleId())
            .append("inputCost", getInputCost())
            .append("processingCost", getProcessingCost())
            .append("outputCost", getOutputCost())
            .append("calcTime", getCalcTime())
            .toString();
    }
}
