package com.ruoyi.repackage.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ruoyi.common.annotation.Anonymous;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.repackage.domain.ConversionCostConvolution;
import com.ruoyi.repackage.service.IConversionCostConvolutionService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 分装成本卷积Controller
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
@RestController
@RequestMapping("/repackage/convolution")
public class ConversionCostConvolutionController extends BaseController
{
    @Autowired
    private IConversionCostConvolutionService conversionCostConvolutionService;

    /**
     * 查询分装成本卷积列表
     */
    @PreAuthorize("@ss.hasPermi('repackage:convolution:list')")
    @GetMapping("/list")
    public TableDataInfo list(ConversionCostConvolution conversionCostConvolution)
    {
        startPage();
        List<ConversionCostConvolution> list = conversionCostConvolutionService.selectConversionCostConvolutionList(conversionCostConvolution);
        return getDataTable(list);
    }

    /**
     * 导出分装成本卷积列表
     */
    @PreAuthorize("@ss.hasPermi('repackage:convolution:export')")
    @Log(title = "分装成本卷积", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, ConversionCostConvolution conversionCostConvolution)
    {
        List<ConversionCostConvolution> list = conversionCostConvolutionService.selectConversionCostConvolutionList(conversionCostConvolution);
        ExcelUtil<ConversionCostConvolution> util = new ExcelUtil<ConversionCostConvolution>(ConversionCostConvolution.class);
        util.exportExcel(response, list, "分装成本卷积数据");
    }

    /**
     * 获取分装成本卷积详细信息
     */
    @PreAuthorize("@ss.hasPermi('repackage:convolution:query')")
    @GetMapping(value = "/{convolutionId}")
    public AjaxResult getInfo(@PathVariable("convolutionId") Long convolutionId)
    {
        return success(conversionCostConvolutionService.selectConversionCostConvolutionByConvolutionId(convolutionId));
    }

    /**
     * 新增分装成本卷积
     */
    @PreAuthorize("@ss.hasPermi('repackage:convolution:add')")
    @Log(title = "分装成本卷积", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ConversionCostConvolution conversionCostConvolution)
    {
        return toAjax(conversionCostConvolutionService.insertConversionCostConvolution(conversionCostConvolution));
    }

    /**
     * 修改分装成本卷积
     */
    @PreAuthorize("@ss.hasPermi('repackage:convolution:edit')")
    @Log(title = "分装成本卷积", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ConversionCostConvolution conversionCostConvolution)
    {
        return toAjax(conversionCostConvolutionService.updateConversionCostConvolution(conversionCostConvolution));
    }

    /**
     * 删除分装成本卷积
     */
    @PreAuthorize("@ss.hasPermi('repackage:convolution:remove')")
    @Log(title = "分装成本卷积", businessType = BusinessType.DELETE)
	@DeleteMapping("/{convolutionIds}")
    public AjaxResult remove(@PathVariable Long[] convolutionIds)
    {
        return toAjax(conversionCostConvolutionService.deleteConversionCostConvolutionByConvolutionIds(convolutionIds));
    }

    @Anonymous
    @GetMapping("/avgCost")
    public void avgCost(String yearMonth ,String type)
    {
        try {
            conversionCostConvolutionService.calculateAverageCostMonth(yearMonth, type);
        } catch (JsonProcessingException e) {
            e.printStackTrace();
        }
    }
}
