package com.ruoyi.repackage.mapper;

import com.ruoyi.repackage.domain.InventoryIssue;

import java.util.List;

/**
 * 本期出库明细 Current period inventory issuesMapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-09
 */
public interface InventoryIssueMapper
{
    /**
     * 查询本期出库明细 Current period inventory issues
     * 
     * @param id 本期出库明细 Current period inventory issues主键
     * @return 本期出库明细 Current period inventory issues
     */
    public InventoryIssue selectInventoryCurrentPeriodIssueById(Long id);

    /**
     * 查询本期出库明细 Current period inventory issues列表
     * 
     * @param inventoryIssue 本期出库明细 Current period inventory issues
     * @return 本期出库明细 Current period inventory issues集合
     */
    public List<InventoryIssue> selectInventoryCurrentPeriodIssueList(InventoryIssue inventoryIssue);

    /**
     * 新增本期出库明细 Current period inventory issues
     *
     * @param inventoryIssue 本期出库明细 Current period inventory issues
     * @return 结果
     */
    public int insertInventoryCurrentPeriodIssue(InventoryIssue inventoryIssue);

    /**
     * 批量新增本期出库明细 Current period inventory issues
     *
     * @param inventoryIssues 本期出库明细 Current period inventory issues列表
     * @return 结果
     */
    public int batchInsertInventoryCurrentPeriodIssue(List<InventoryIssue> inventoryIssues);

    /**
     * 修改本期出库明细 Current period inventory issues
     * 
     * @param inventoryIssue 本期出库明细 Current period inventory issues
     * @return 结果
     */
    public int updateInventoryCurrentPeriodIssue(InventoryIssue inventoryIssue);

    /**
     * 删除本期出库明细 Current period inventory issues
     * 
     * @param id 本期出库明细 Current period inventory issues主键
     * @return 结果
     */
    public int deleteInventoryCurrentPeriodIssueById(Long id);

    /**
     * 批量删除本期出库明细 Current period inventory issues
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryCurrentPeriodIssueByIds(Long[] ids);

    /**
     * 根据单据类型查本期出库明细 Current period inventory issues
     *
     * @param document_type 需要删除的数据主键集合
     * @return InventoryCurrentPeriodIssue
     */
    List<InventoryIssue> selectInventoryCurrentPeriodIssueByType(InventoryIssue inventoryIssue);
}
