package com.ruoyi.repackage.mapper;

import com.ruoyi.repackage.domain.InventoryEntry;

import java.util.List;

/**
 * 本期入库明细 Current period inventory entriesMapper接口
 * 
 * <AUTHOR>
 * @date 2025-06-06
 */
public interface InventoryEntryMapper
{
    /**
     * 查询本期入库明细 Current period inventory entries
     * 
     * @param id 本期入库明细 Current period inventory entries主键
     * @return 本期入库明细 Current period inventory entries
     */
    public InventoryEntry selectInventoryCurrentPeriodEntryById(Long id);

    /**
     * 查询本期入库明细 Current period inventory entries列表
     * 
     * @param inventoryEntry 本期入库明细 Current period inventory entries
     * @return 本期入库明细 Current period inventory entries集合
     */
    public List<InventoryEntry> selectInventoryCurrentPeriodEntryList(InventoryEntry inventoryEntry);

    /**
     * 批量新增本期入库明细 Current period inventory entries
     *
     * @param inventoryCurrentPeriodIssues 本期出库明细 Current period inventory issues列表
     * @return 结果
     */
    public int batchInsertInventoryCurrentPeriodEntry(List<InventoryEntry> inventoryCurrentPeriodIssues);

    /**
     * 新增本期入库明细 Current period inventory entries
     * 
     * @param inventoryEntry 本期入库明细 Current period inventory entries
     * @return 结果
     */
    public int insertInventoryCurrentPeriodEntry(InventoryEntry inventoryEntry);

    /**
     * 修改本期入库明细 Current period inventory entries
     * 
     * @param inventoryEntry 本期入库明细 Current period inventory entries
     * @return 结果
     */
    public int updateInventoryCurrentPeriodEntry(InventoryEntry inventoryEntry);

    /**
     * 删除本期入库明细 Current period inventory entries
     * 
     * @param id 本期入库明细 Current period inventory entries主键
     * @return 结果
     */
    public int deleteInventoryCurrentPeriodEntryById(Long id);

    /**
     * 批量删除本期入库明细 Current period inventory entries
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteInventoryCurrentPeriodEntryByIds(Long[] ids);

    public List<InventoryEntry> selectInventoryCurrentPeriodEntryBySkuAndPeriod(InventoryEntry inventoryEntry);
}
