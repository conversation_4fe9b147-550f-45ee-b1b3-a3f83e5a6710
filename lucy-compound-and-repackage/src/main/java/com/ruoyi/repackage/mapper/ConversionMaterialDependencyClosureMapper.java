package com.ruoyi.repackage.mapper;

import java.util.List;
import java.util.Set;

import com.ruoyi.repackage.domain.ConversionMaterialDependencyClosure;
import io.lettuce.core.dynamic.annotation.Param;

/**
 * 物料依赖图谱Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface ConversionMaterialDependencyClosureMapper 
{
    /**
     * 查询物料依赖图谱
     * 
     * @param ancestorSku 物料依赖图谱主键
     * @return 物料依赖图谱
     */
    public ConversionMaterialDependencyClosure selectConversionMaterialDependencyClosureByAncestorSku(String ancestorSku);

    /**
     * 查询物料依赖图谱列表
     * 
     * @param conversionMaterialDependencyClosure 物料依赖图谱
     * @return 物料依赖图谱集合
     */
    public List<ConversionMaterialDependencyClosure> selectConversionMaterialDependencyClosureList(ConversionMaterialDependencyClosure conversionMaterialDependencyClosure);

    /**
     * 新增物料依赖图谱
     * 
     * @param conversionMaterialDependencyClosure 物料依赖图谱
     * @return 结果
     */
    public int insertConversionMaterialDependencyClosure(ConversionMaterialDependencyClosure conversionMaterialDependencyClosure);

    /**
     * 修改物料依赖图谱
     * 
     * @param conversionMaterialDependencyClosure 物料依赖图谱
     * @return 结果
     */
    public int updateConversionMaterialDependencyClosure(ConversionMaterialDependencyClosure conversionMaterialDependencyClosure);

    /**
     * 删除物料依赖图谱
     * 
     * @param ancestorSku 物料依赖图谱主键
     * @return 结果
     */
    public int deleteConversionMaterialDependencyClosureByAncestorSku(String ancestorSku);

    /**
     * 批量删除物料依赖图谱
     *
     * @param ancestorSkus 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteConversionMaterialDependencyClosureByAncestorSkus(String[] ancestorSkus);

    ConversionMaterialDependencyClosure selectConversionMaterialDependencyClosureBySku(String ancestorSku, String descendantSku);

    List<ConversionMaterialDependencyClosure> selectConversionMaterialDependencyClosureByDescendantSku(Set<String> descendantSku);

    List<ConversionMaterialDependencyClosure> selectConversionMaterialDependencyClosureAll();

    /**
     * 清空整个物料依赖图谱表
     *
     * @return 结果
     */
    public int deleteAllConversionMaterialDependencyClosure();

    /**
     * 批量插入物料依赖图谱
     *
     * @param closures 物料依赖图谱列表
     * @return 结果
     */
    public int batchInsertConversionMaterialDependencyClosure(List<ConversionMaterialDependencyClosure> closures);
}
