package com.ruoyi.repackage.mapper;

import java.util.List;
import com.ruoyi.repackage.domain.ConversionDependencyCycle;

/**
 * 环状依赖记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-11
 */
public interface ConversionDependencyCycleMapper 
{
    /**
     * 查询环状依赖记录
     * 
     * @param cycleId 环状依赖记录主键
     * @return 环状依赖记录
     */
    public ConversionDependencyCycle selectConversionDependencyCycleByCycleId(Long cycleId);

    /**
     * 查询环状依赖记录列表
     * 
     * @param conversionDependencyCycle 环状依赖记录
     * @return 环状依赖记录集合
     */
    public List<ConversionDependencyCycle> selectConversionDependencyCycleList(ConversionDependencyCycle conversionDependencyCycle);

    /**
     * 新增环状依赖记录
     * 
     * @param conversionDependencyCycle 环状依赖记录
     * @return 结果
     */
    public int insertConversionDependencyCycle(ConversionDependencyCycle conversionDependencyCycle);

    /**
     * 修改环状依赖记录
     * 
     * @param conversionDependencyCycle 环状依赖记录
     * @return 结果
     */
    public int updateConversionDependencyCycle(ConversionDependencyCycle conversionDependencyCycle);

    /**
     * 删除环状依赖记录
     * 
     * @param cycleId 环状依赖记录主键
     * @return 结果
     */
    public int deleteConversionDependencyCycleByCycleId(Long cycleId);

    /**
     * 批量删除环状依赖记录
     *
     * @param cycleIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteConversionDependencyCycleByCycleIds(Long[] cycleIds);

    public List<ConversionDependencyCycle> selectConversionDependencyCycleAll();

    /**
     * 删除所有环状依赖记录
     *
     * @return 结果
     */
    public int deleteAllConversionDependencyCycle();
}
