<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.repackage.mapper.ConversionTransactionMapper">
    
    <resultMap type="ConversionTransaction" id="ConversionTransactionResult">
        <result property="transId"    column="trans_id"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="inputSku"    column="input_sku"    />
        <result property="outputSku"    column="output_sku"    />
        <result property="inputQuantity"    column="input_quantity"    />
        <result property="outputQuantity"    column="output_quantity"    />
        <result property="actualLossRate"    column="actual_loss_rate"    />
        <result property="transactionDate"    column="transaction_date"    />
        <result property="monthKey"    column="month_key"    />
        <result property="transBizcd"    column="trans_bizcd"    />
        <result property="costAllocated"    column="cost_allocated"    />
        <result property="assetOwner"    column="asset_owner"    />
    </resultMap>

    <sql id="selectConversionTransactionVo">
        select trans_id, rule_id, input_sku, output_sku, input_quantity, output_quantity, actual_loss_rate, transaction_date, month_key, trans_bizcd, cost_allocated, asset_owner from conversion_transaction
    </sql>

    <select id="selectConversionTransactionList" parameterType="ConversionTransaction" resultMap="ConversionTransactionResult">
        <include refid="selectConversionTransactionVo"/>
        <where>  
            <if test="ruleId != null "> and rule_id = #{ruleId}</if>
            <if test="inputSku != null  and inputSku != ''"> and input_sku = #{inputSku}</if>
            <if test="outputSku != null  and outputSku != ''"> and output_sku = #{outputSku}</if>
            <if test="inputQuantity != null "> and input_quantity = #{inputQuantity}</if>
            <if test="outputQuantity != null "> and output_quantity = #{outputQuantity}</if>
            <if test="actualLossRate != null "> and actual_loss_rate = #{actualLossRate}</if>
            <if test="transactionDate != null "> and transaction_date = #{transactionDate}</if>
            <if test="monthKey != null  and monthKey != ''"> and month_key = #{monthKey}</if>
            <if test="transBizcd != null  and transBizcd != ''"> and trans_bizcd = #{transBizcd}</if>
            <if test="costAllocated != null "> and cost_allocated = #{costAllocated}</if>
            <if test="assetOwner != null "> and asset_owner = #{assetOwner}</if>
        </where>
    </select>
    
    <select id="selectConversionTransactionByTransId" parameterType="Long" resultMap="ConversionTransactionResult">
        <include refid="selectConversionTransactionVo"/>
        where trans_id = #{transId}
    </select>

    <insert id="insertConversionTransaction" parameterType="ConversionTransaction" useGeneratedKeys="true" keyProperty="transId">
        insert into conversion_transaction
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">rule_id,</if>
            <if test="inputSku != null and inputSku != ''">input_sku,</if>
            <if test="outputSku != null and outputSku != ''">output_sku,</if>
            <if test="inputQuantity != null">input_quantity,</if>
            <if test="outputQuantity != null">output_quantity,</if>
            <if test="actualLossRate != null">actual_loss_rate,</if>
            <if test="transactionDate != null">transaction_date,</if>
            <if test="monthKey != null">month_key,</if>
            <if test="transBizcd != null and transBizcd != ''">trans_bizcd,</if>
            <if test="costAllocated != null">cost_allocated,</if>
            <if test="assetOwner != null ">asset_owner,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="ruleId != null">#{ruleId},</if>
            <if test="inputSku != null and inputSku != ''">#{inputSku},</if>
            <if test="outputSku != null and outputSku != ''">#{outputSku},</if>
            <if test="inputQuantity != null">#{inputQuantity},</if>
            <if test="outputQuantity != null">#{outputQuantity},</if>
            <if test="actualLossRate != null">#{actualLossRate},</if>
            <if test="transactionDate != null">#{transactionDate},</if>
            <if test="monthKey != null">#{monthKey},</if>
            <if test="transBizcd != null and transBizcd != ''">#{transBizcd},</if>
            <if test="costAllocated != null">#{costAllocated},</if>
            <if test="assetOwner != null ">#{assetOwner},</if>
         </trim>
    </insert>

    <update id="updateConversionTransaction" parameterType="ConversionTransaction">
        update conversion_transaction
        <trim prefix="SET" suffixOverrides=",">
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="inputSku != null and inputSku != ''">input_sku = #{inputSku},</if>
            <if test="outputSku != null and outputSku != ''">output_sku = #{outputSku},</if>
            <if test="inputQuantity != null">input_quantity = #{inputQuantity},</if>
            <if test="outputQuantity != null">output_quantity = #{outputQuantity},</if>
            <if test="actualLossRate != null">actual_loss_rate = #{actualLossRate},</if>
            <if test="transactionDate != null">transaction_date = #{transactionDate},</if>
            <if test="monthKey != null">month_key = #{monthKey},</if>
            <if test="transBizcd != null and transBizcd != ''">trans_bizcd = #{transBizcd},</if>
            <if test="costAllocated != null">cost_allocated = #{costAllocated},</if>
            <if test="assetOwner != null">asset_owner = #{assetOwner},</if>
        </trim>
        where trans_id = #{transId}
    </update>

    <delete id="deleteConversionTransactionByTransId" parameterType="Long">
        delete from conversion_transaction where trans_id = #{transId}
    </delete>

    <delete id="deleteConversionTransactionByTransIds" parameterType="String">
        delete from conversion_transaction where trans_id in 
        <foreach item="transId" collection="array" open="(" separator="," close=")">
            #{transId}
        </foreach>
    </delete>

    <select id="selectConversionTransactionBySku" parameterType="string" resultMap="ConversionTransactionResult">
        <include refid="selectConversionTransactionVo"/>
        where output_sku in
        <foreach item="item" collection="sku" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>
</mapper>