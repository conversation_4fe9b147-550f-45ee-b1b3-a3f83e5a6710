<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.repackage.mapper.ConversionCostConvolutionMapper">
    
    <resultMap type="ConversionCostConvolution" id="ConversionCostConvolutionResult">
        <result property="convolutionId"    column="convolution_id"    />
        <result property="monthKey"    column="month_key"    />
        <result property="targetSku"    column="target_sku"    />
        <result property="depth"    column="depth"    />
        <result property="ruleId"    column="rule_id"    />
        <result property="inputCost"    column="input_cost"    />
        <result property="processingCost"    column="processing_cost"    />
        <result property="outputCost"    column="output_cost"    />
        <result property="calcTime"    column="calc_time"    />
        <result property="assetOwner"    column="asset_owner"    />
    </resultMap>

    <sql id="selectConversionCostConvolutionVo">
        select convolution_id, month_key, target_sku, depth, rule_id, input_cost, processing_cost, output_cost, calc_time, asset_owner from conversion_cost_convolution
    </sql>

    <select id="selectConversionCostConvolutionList" parameterType="ConversionCostConvolution" resultMap="ConversionCostConvolutionResult">
        <include refid="selectConversionCostConvolutionVo"/>
        <where>  
            <if test="monthKey != null  and monthKey != ''"> and month_key = #{monthKey}</if>
            <if test="targetSku != null  and targetSku != ''"> and target_sku = #{targetSku}</if>
            <if test="depth != null "> and depth = #{depth}</if>
            <if test="ruleId != null "> and rule_id = #{ruleId}</if>
            <if test="inputCost != null "> and input_cost = #{inputCost}</if>
            <if test="processingCost != null "> and processing_cost = #{processingCost}</if>
            <if test="outputCost != null "> and output_cost = #{outputCost}</if>
            <if test="calcTime != null "> and calc_time = #{calcTime}</if>
            <if test="assetOwner != null "> and asset_owner = #{assetOwner}</if>
        </where>
    </select>
    
    <select id="selectConversionCostConvolutionByConvolutionId" parameterType="Long" resultMap="ConversionCostConvolutionResult">
        <include refid="selectConversionCostConvolutionVo"/>
        where convolution_id = #{convolutionId}
    </select>

    <insert id="insertConversionCostConvolution" parameterType="ConversionCostConvolution" useGeneratedKeys="true" keyProperty="convolutionId">
        insert into conversion_cost_convolution
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="monthKey != null and monthKey != ''">month_key,</if>
            <if test="targetSku != null and targetSku != ''">target_sku,</if>
            <if test="depth != null">depth,</if>
            <if test="ruleId != null">rule_id,</if>
            <if test="inputCost != null">input_cost,</if>
            <if test="processingCost != null">processing_cost,</if>
            <if test="outputCost != null">output_cost,</if>
            <if test="calcTime != null">calc_time,</if>
            <if test="assetOwner != null ">asset_owner,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="monthKey != null and monthKey != ''">#{monthKey},</if>
            <if test="targetSku != null and targetSku != ''">#{targetSku},</if>
            <if test="depth != null">#{depth},</if>
            <if test="ruleId != null">#{ruleId},</if>
            <if test="inputCost != null">#{inputCost},</if>
            <if test="processingCost != null">#{processingCost},</if>
            <if test="outputCost != null">#{outputCost},</if>
            <if test="calcTime != null">#{calcTime},</if>
            <if test="assetOwner != null ">#{assetOwner},</if>
         </trim>
    </insert>

    <update id="updateConversionCostConvolution" parameterType="ConversionCostConvolution">
        update conversion_cost_convolution
        <trim prefix="SET" suffixOverrides=",">
            <if test="monthKey != null and monthKey != ''">month_key = #{monthKey},</if>
            <if test="targetSku != null and targetSku != ''">target_sku = #{targetSku},</if>
            <if test="depth != null">depth = #{depth},</if>
            <if test="ruleId != null">rule_id = #{ruleId},</if>
            <if test="inputCost != null">input_cost = #{inputCost},</if>
            <if test="processingCost != null">processing_cost = #{processingCost},</if>
            <if test="outputCost != null">output_cost = #{outputCost},</if>
            <if test="calcTime != null">calc_time = #{calcTime},</if>
            <if test="assetOwner != null "> and asset_owner = #{assetOwner},</if>
        </trim>
        where convolution_id = #{convolutionId}
    </update>

    <delete id="deleteConversionCostConvolutionByConvolutionId" parameterType="Long">
        delete from conversion_cost_convolution where convolution_id = #{convolutionId}
    </delete>

    <delete id="deleteConversionCostConvolutionByConvolutionIds" parameterType="String">
        delete from conversion_cost_convolution where convolution_id in 
        <foreach item="convolutionId" collection="array" open="(" separator="," close=")">
            #{convolutionId}
        </foreach>
    </delete>
</mapper>