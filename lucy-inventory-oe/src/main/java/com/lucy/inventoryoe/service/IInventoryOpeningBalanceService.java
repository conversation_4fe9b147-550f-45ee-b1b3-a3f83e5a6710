package com.lucy.inventoryoe.service;

import java.util.List;
import java.util.Set;

import com.lucy.inventoryoe.domain.InventoryOpeningBalance;

/**
 * 库存期初汇总 Inventory opening balance summaryService接口
 * 
 * <AUTHOR>
 * @date 2025-05-29
 */
public interface IInventoryOpeningBalanceService 
{
    /**
     * 查询库存期初汇总 Inventory opening balance summary
     * 
     * @param id 库存期初汇总 Inventory opening balance summary主键
     * @return 库存期初汇总 Inventory opening balance summary
     */
    public InventoryOpeningBalance selectInventoryOpeningBalanceById(Long id);

    /**
     * 查询库存期初汇总 Inventory opening balance summary列表
     * 
     * @param inventoryOpeningBalance 库存期初汇总 Inventory opening balance summary
     * @return 库存期初汇总 Inventory opening balance summary集合
     */
    public List<InventoryOpeningBalance> selectInventoryOpeningBalanceList(InventoryOpeningBalance inventoryOpeningBalance);

    /**
     * 新增库存期初汇总 Inventory opening balance summary
     * 
     * @param inventoryOpeningBalance 库存期初汇总 Inventory opening balance summary
     * @return 结果
     */
    public int insertInventoryOpeningBalance(InventoryOpeningBalance inventoryOpeningBalance);

    /**
     * 修改库存期初汇总 Inventory opening balance summary
     * 
     * @param inventoryOpeningBalance 库存期初汇总 Inventory opening balance summary
     * @return 结果
     */
    public int updateInventoryOpeningBalance(InventoryOpeningBalance inventoryOpeningBalance);

    /**
     * 批量删除库存期初汇总 Inventory opening balance summary
     * 
     * @param ids 需要删除的库存期初汇总 Inventory opening balance summary主键集合
     * @return 结果
     */
    public int deleteInventoryOpeningBalanceByIds(Long[] ids);

    /**
     * 删除库存期初汇总 Inventory opening balance summary信息
     * 
     * @param id 库存期初汇总 Inventory opening balance summary主键
     * @return 结果
     */
    public int deleteInventoryOpeningBalanceById(Long id);

    public List<InventoryOpeningBalance> selectInventoryOpeningBalanceBySkuAndAccountingPeriod(Set<String> skuSet, String accountingPeriod, String assetOwner);
}
