<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.inventoryentry.mapper.InventoryCurrentPeriodEntryMapper">
    
    <resultMap type="InventoryEntry" id="InventoryCurrentPeriodEntryResult">
        <result property="id"    column="id"    />
        <result property="accountingPeriod"    column="accounting_period"    />
        <result property="financialAccount"    column="financial_account"    />
        <result property="sku"    column="sku"    />
        <result property="quantity"    column="quantity"    />
        <result property="amount"    column="amount"    />
        <result property="sourceType"    column="source_type"    />
        <result property="purchaseScope"    column="purchase_scope"    />
        <result property="supplierCode"    column="supplier_code"    />
        <result property="supplierName"    column="supplier_name"    />
        <result property="batchNumber"    column="batch_number"    />
        <result property="warehouse"    column="warehouse"    />
        <result property="storageLocation"    column="storage_location"    />
        <result property="storageZone"    column="storage_zone"    />
        <result property="sourceTable"    column="source_table"    />
        <result property="sourceRecordId"    column="source_record_id"    />
        <result property="entryTime"    column="entry_time"    />
        <result property="operator"    column="operator"    />
        <result property="auditor"    column="auditor"    />
        <result property="auditTime"    column="audit_time"    />
        <result property="encryptedBatch"    column="encrypted_batch"    />
        <result property="documentType"    column="document_type"    />
        <result property="assetOwner"    column="asset_owner"    />
        <result property="processor"    column="processor"    />
        <result property="processTime"    column="process_time"    />
        <result property="department"    column="department"    />
        <result property="departmentAuditor"    column="department_auditor"    />
        <result property="departmentAuditTime"    column="department_audit_time"    />
        <result property="siteCode"    column="site_code"    />
        <result property="projectCode"    column="project_code"    />
        <result property="projectName"    column="project_name"    />
        <result property="rdSystemId"    column="rd_system_id"    />
        <result property="createdBy"    column="created_by"    />
        <result property="createdTime"    column="created_time"    />
        <result property="updatedBy"    column="updated_by"    />
        <result property="updatedTime"    column="updated_time"    />
        <result property="version"    column="version"    />
        <result property="isDeleted"    column="is_deleted"    />
    </resultMap>

    <sql id="selectInventoryCurrentPeriodEntryVo">
        select id, accounting_period, financial_account, sku, quantity, amount, source_type, purchase_scope, supplier_code, supplier_name, batch_number, warehouse, storage_location, storage_zone, source_table, source_record_id, entry_time, operator, auditor, audit_time, encrypted_batch, document_type, asset_owner, processor, process_time, department, department_auditor, department_audit_time, site_code, project_code, project_name, rd_system_id, created_by, created_time, updated_by, updated_time, version, is_deleted from inventory_current_period_entry
    </sql>

    <select id="selectInventoryCurrentPeriodEntryList" parameterType="InventoryCurrentPeriodEntry" resultMap="InventoryCurrentPeriodEntryResult">
        <include refid="selectInventoryCurrentPeriodEntryVo"/>
        <where>  
            <if test="accountingPeriod != null  and accountingPeriod != ''"> and accounting_period = #{accountingPeriod}</if>
            <if test="financialAccount != null  and financialAccount != ''"> and financial_account = #{financialAccount}</if>
            <if test="sku != null  and sku != ''"> and sku = #{sku}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="sourceType != null  and sourceType != ''"> and source_type = #{sourceType}</if>
            <if test="purchaseScope != null  and purchaseScope != ''"> and purchase_scope = #{purchaseScope}</if>
            <if test="supplierCode != null  and supplierCode != ''"> and supplier_code = #{supplierCode}</if>
            <if test="supplierName != null  and supplierName != ''"> and supplier_name like concat('%', #{supplierName}, '%')</if>
            <if test="batchNumber != null  and batchNumber != ''"> and batch_number = #{batchNumber}</if>
            <if test="warehouse != null  and warehouse != ''"> and warehouse = #{warehouse}</if>
            <if test="storageLocation != null  and storageLocation != ''"> and storage_location = #{storageLocation}</if>
            <if test="storageZone != null  and storageZone != ''"> and storage_zone = #{storageZone}</if>
            <if test="sourceTable != null  and sourceTable != ''"> and source_table = #{sourceTable}</if>
            <if test="sourceRecordId != null "> and source_record_id = #{sourceRecordId}</if>
            <if test="entryTime != null "> and entry_time = #{entryTime}</if>
            <if test="operator != null  and operator != ''"> and operator = #{operator}</if>
            <if test="auditor != null  and auditor != ''"> and auditor = #{auditor}</if>
            <if test="auditTime != null "> and audit_time = #{auditTime}</if>
            <if test="encryptedBatch != null  and encryptedBatch != ''"> and encrypted_batch = #{encryptedBatch}</if>
            <if test="documentType != null  and documentType != ''"> and document_type = #{documentType}</if>
            <if test="assetOwner != null  and assetOwner != ''"> and asset_owner = #{assetOwner}</if>
            <if test="processor != null  and processor != ''"> and processor = #{processor}</if>
            <if test="processTime != null "> and process_time = #{processTime}</if>
            <if test="department != null  and department != ''"> and department = #{department}</if>
            <if test="departmentAuditor != null  and departmentAuditor != ''"> and department_auditor = #{departmentAuditor}</if>
            <if test="departmentAuditTime != null "> and department_audit_time = #{departmentAuditTime}</if>
            <if test="siteCode != null  and siteCode != ''"> and site_code = #{siteCode}</if>
            <if test="projectCode != null  and projectCode != ''"> and project_code = #{projectCode}</if>
            <if test="projectName != null  and projectName != ''"> and project_name like concat('%', #{projectName}, '%')</if>
            <if test="rdSystemId != null  and rdSystemId != ''"> and rd_system_id = #{rdSystemId}</if>
            <if test="createdBy != null  and createdBy != ''"> and created_by = #{createdBy}</if>
            <if test="createdTime != null "> and created_time = #{createdTime}</if>
            <if test="updatedBy != null  and updatedBy != ''"> and updated_by = #{updatedBy}</if>
            <if test="updatedTime != null "> and updated_time = #{updatedTime}</if>
            <if test="version != null "> and version = #{version}</if>
            <if test="isDeleted != null "> and is_deleted = #{isDeleted}</if>
             <if test="documentTypeList != null and documentTypeList.size() > 0">
                and document_type in
                <foreach item="item" index="index" collection="documentTypeList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>
    
    <select id="selectInventoryCurrentPeriodEntryById" parameterType="Long" resultMap="InventoryCurrentPeriodEntryResult">
        <include refid="selectInventoryCurrentPeriodEntryVo"/>
        where id = #{id}
    </select>

    <insert id="batchInsertInventoryCurrentPeriodEntry" parameterType="java.util.List">
        INSERT INTO inventory_current_period_entry
        (accounting_period,financial_account,sku,quantity,amount,source_type,purchase_scope,supplier_code,supplier_name,batch_number,warehouse,
        storage_location,storage_zone,source_table,source_record_id,entry_time,operator,auditor,audit_time,encrypted_batch,document_type,
        asset_owner,processor,process_time, department,department_auditor,department_audit_time,site_code,project_code,project_name,rd_system_id,
        created_by,updated_by)
        values
        <foreach collection="list" item="item" separator=",">
            (
            #{item.accountingPeriod},#{item.financialAccount},#{item.sku},#{item.quantity},#{item.amount},#{item.sourceType},#{item.purchaseScope},
            #{item.supplierCode},#{item.supplierName},#{item.batchNumber},#{item.warehouse},#{item.storageLocation},#{item.storageZone},#{item.sourceTable},
            #{item.sourceRecordId},#{item.entryTime},#{item.operator},#{item.auditor},#{item.auditTime},#{item.encryptedBatch},#{item.documentType},
            #{item.assetOwner},#{item.processor},#{item.processTime},#{item.department},#{item.departmentAuditor},#{item.departmentAuditTime},
            #{item.siteCode},#{item.projectCode},#{item.projectName},#{item.rdSystemId},#{item.createdBy},#{item.updatedBy}
            )
        </foreach>
    </insert>

    <insert id="insertInventoryCurrentPeriodEntry" parameterType="InventoryEntry" useGeneratedKeys="true" keyProperty="id">
        insert into inventory_current_period_entry
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period,</if>
            <if test="financialAccount != null and financialAccount != ''">financial_account,</if>
            <if test="sku != null and sku != ''">sku,</if>
            <if test="quantity != null">quantity,</if>
            <if test="amount != null">amount,</if>
            <if test="sourceType != null and sourceType != ''">source_type,</if>
            <if test="purchaseScope != null">purchase_scope,</if>
            <if test="supplierCode != null">supplier_code,</if>
            <if test="supplierName != null">supplier_name,</if>
            <if test="batchNumber != null and batchNumber != ''">batch_number,</if>
            <if test="warehouse != null and warehouse != ''">warehouse,</if>
            <if test="storageLocation != null and storageLocation != ''">storage_location,</if>
            <if test="storageZone != null and storageZone != ''">storage_zone,</if>
            <if test="sourceTable != null and sourceTable != ''">source_table,</if>
            <if test="sourceRecordId != null">source_record_id,</if>
            <if test="entryTime != null">entry_time,</if>
            <if test="operator != null">operator,</if>
            <if test="auditor != null">auditor,</if>
            <if test="auditTime != null">audit_time,</if>
            <if test="encryptedBatch != null and encryptedBatch != ''">encrypted_batch,</if>
            <if test="documentType != null">document_type,</if>
            <if test="assetOwner != null and assetOwner != ''">asset_owner,</if>
            <if test="processor != null">processor,</if>
            <if test="processTime != null">process_time,</if>
            <if test="department != null and department != ''">department,</if>
            <if test="departmentAuditor != null">department_auditor,</if>
            <if test="departmentAuditTime != null">department_audit_time,</if>
            <if test="siteCode != null">site_code,</if>
            <if test="projectCode != null">project_code,</if>
            <if test="projectName != null">project_name,</if>
            <if test="rdSystemId != null">rd_system_id,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="createdTime != null">created_time,</if>
            <if test="updatedBy != null">updated_by,</if>
            <if test="updatedTime != null">updated_time,</if>
            <if test="version != null">version,</if>
            <if test="isDeleted != null">is_deleted,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">#{accountingPeriod},</if>
            <if test="financialAccount != null and financialAccount != ''">#{financialAccount},</if>
            <if test="sku != null and sku != ''">#{sku},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="amount != null">#{amount},</if>
            <if test="sourceType != null and sourceType != ''">#{sourceType},</if>
            <if test="purchaseScope != null and purchaseScope != ''">#{purchaseScope},</if>
            <if test="supplierCode != null">#{supplierCode},</if>
            <if test="supplierName != null">#{supplierName},</if>
            <if test="batchNumber != null and batchNumber != ''">#{batchNumber},</if>
            <if test="warehouse != null and warehouse != ''">#{warehouse},</if>
            <if test="storageLocation != null and storageLocation != ''">#{storageLocation},</if>
            <if test="storageZone != null and storageZone != ''">#{storageZone},</if>
            <if test="sourceTable != null and sourceTable != ''">#{sourceTable},</if>
            <if test="sourceRecordId != null">#{sourceRecordId},</if>
            <if test="entryTime != null">#{entryTime},</if>
            <if test="operator != null">#{operator},</if>
            <if test="auditor != null">#{auditor},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="encryptedBatch != null and encryptedBatch != ''">#{encryptedBatch},</if>
            <if test="documentType != null">#{documentType},</if>
            <if test="assetOwner != null and assetOwner != ''">#{assetOwner},</if>
            <if test="processor != null">#{processor},</if>
            <if test="processTime != null">#{processTime},</if>
            <if test="department != null and department != ''">#{department},</if>
            <if test="departmentAuditor != null">#{departmentAuditor},</if>
            <if test="departmentAuditTime != null">#{departmentAuditTime},</if>
            <if test="siteCode != null">#{siteCode},</if>
            <if test="projectCode != null">#{projectCode},</if>
            <if test="projectName != null">#{projectName},</if>
            <if test="rdSystemId != null">#{rdSystemId},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="createdTime != null">#{createdTime},</if>
            <if test="updatedBy != null">#{updatedBy},</if>
            <if test="updatedTime != null">#{updatedTime},</if>
            <if test="version != null">#{version},</if>
            <if test="isDeleted != null">#{isDeleted},</if>
         </trim>
    </insert>

    <update id="updateInventoryCurrentPeriodEntry" parameterType="InventoryCurrentPeriodEntry">
        update inventory_current_period_entry
        <trim prefix="SET" suffixOverrides=",">
            <if test="accountingPeriod != null and accountingPeriod != ''">accounting_period = #{accountingPeriod},</if>
            <if test="financialAccount != null and financialAccount != ''">financial_account = #{financialAccount},</if>
            <if test="sku != null and sku != ''">sku = #{sku},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="sourceType != null and sourceType != ''">source_type = #{sourceType},</if>
            <if test="purchaseScope != null and purchaseScope != ''">purchase_scope = #{purchaseScope},</if>
            <if test="supplierCode != null and supplierCode != ''">supplier_code = #{supplierCode},</if>
            <if test="supplierName != null and supplierName != ''">supplier_name = #{supplierName},</if>
            <if test="batchNumber != null and batchNumber != ''">batch_number = #{batchNumber},</if>
            <if test="warehouse != null and warehouse != ''">warehouse = #{warehouse},</if>
            <if test="storageLocation != null and storageLocation != ''">storage_location = #{storageLocation},</if>
            <if test="storageZone != null and storageZone != ''">storage_zone = #{storageZone},</if>
            <if test="sourceTable != null and sourceTable != ''">source_table = #{sourceTable},</if>
            <if test="sourceRecordId != null">source_record_id = #{sourceRecordId},</if>
            <if test="entryTime != null">entry_time = #{entryTime},</if>
            <if test="operator != null and operator != ''">operator = #{operator},</if>
            <if test="auditor != null">auditor = #{auditor},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="encryptedBatch != null and encryptedBatch != ''">encrypted_batch = #{encryptedBatch},</if>
            <if test="documentType != null">document_type = #{documentType},</if>
            <if test="assetOwner != null and assetOwner != ''">asset_owner = #{assetOwner},</if>
            <if test="processor != null and processor != ''">processor = #{processor},</if>
            <if test="processTime != null">process_time = #{processTime},</if>
            <if test="department != null and department != ''">department = #{department},</if>
            <if test="departmentAuditor != null">department_auditor = #{departmentAuditor},</if>
            <if test="departmentAuditTime != null">department_audit_time = #{departmentAuditTime},</if>
            <if test="siteCode != null">site_code = #{siteCode},</if>
            <if test="projectCode != null">project_code = #{projectCode},</if>
            <if test="projectName != null">project_name = #{projectName},</if>
            <if test="rdSystemId != null">rd_system_id = #{rdSystemId},</if>
            <if test="createdBy != null and createdBy != ''">created_by = #{createdBy},</if>
            <if test="createdTime != null">created_time = #{createdTime},</if>
            <if test="updatedBy != null and updatedBy != ''">updated_by = #{updatedBy},</if>
            <if test="updatedTime != null">updated_time = #{updatedTime},</if>
            <if test="version != null">version = #{version},</if>
            <if test="isDeleted != null">is_deleted = #{isDeleted},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteInventoryCurrentPeriodEntryById" parameterType="Long">
        delete from inventory_current_period_entry where id = #{id}
    </delete>

    <delete id="deleteInventoryCurrentPeriodEntryByIds" parameterType="String">
        delete from inventory_current_period_entry where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectInventoryCurrentPeriodEntryBySkuAndPeriod" parameterType="InventoryEntry" resultType="InventoryCurrentPeriodEntry">
        <include refid="selectInventoryCurrentPeriodEntryVo"/>
        where accounting_period = #{accountingPeriod}
        and document_type in ("产品生产入库" ,"库存初始化" ,"采购入库" ,"盘盈")
    </select>
</mapper>